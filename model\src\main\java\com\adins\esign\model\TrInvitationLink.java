package com.adins.esign.model;
// Generated Jan 3, 2022 10:59:40 AM by Hibernate Tools 5.2.12.Final

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;

/**
 * TrInvitationLink generated by hbm2java
 */
@Entity
@Table(name = "tr_invitation_link")
public class TrInvitationLink extends ActiveAndUpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	
	public static final String INVITATION_CODE_HBN = "invitationCode";
	public static final String RECEIVER_DETAIL_HBM = "receiverDetail";
	public static final String FULLNAME_HBM = "fullName";
	public static final String IDNO_HBM = "idNo";
	public static final String PHONE_HBM = "phone";
	public static final String EMAIL_HBM = "email";

	private long idInvitationLink;
	private AmMsuser amMsuser;
	private MsTenant msTenant;
	private MsVendor msVendor;
	private MsLov lovUserType;
	private String invitationCode;
	private String invitationBy;
	private String receiverDetail;
	private String otpCode;
	private String gender;
	private String kelurahan;
	private String kecamatan;
	private String kota;
	private String zipCode;
	private Date dateOfBirth;
	private byte[] photoSelf;
	private String placeOfBirth;
	private String provinsi;
	private String email;
	private String idNo;
	private String phone;
	private String address;
	private byte[] photoId;
	private String fullName;
	private String region;
	private String office;
	private String refNumber;
	private String businessLine;
	private MsOffice msOffice;
	private MsBusinessLine msBusinessLine;
	private Date lastRegisterAttempt;
	private Short dailyRegisterAttemptAmount;
	private String isRedirectUrl;
	private String isEmbed;
	private String useSyncPrivyVerif;
	private Short notificationAttemptNum;
	private Date notificationAttemptDate;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_invitation_link", unique = true, nullable = false)
	public long getIdInvitationLink() {
		return this.idInvitationLink;
	}

	public void setIdInvitationLink(long idInvitationLink) {
		this.idInvitationLink = idInvitationLink;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@Column(name = "invitation_code", nullable = false, length = 15)
	public String getInvitationCode() {
		return this.invitationCode;
	}

	public void setInvitationCode(String invitationCode) {
		this.invitationCode = invitationCode;
	}

	@Column(name = "invitation_by", nullable = false, length = 10)
	public String getInvitationBy() {
		return this.invitationBy;
	}

	public void setInvitationBy(String invitationBy) {
		this.invitationBy = invitationBy;
	}

	@Column(name = "receiver_detail", length = 80)
	public String getReceiverDetail() {
		return this.receiverDetail;
	}

	public void setReceiverDetail(String receiverDetail) {
		this.receiverDetail = receiverDetail;
	}

	@Column(name = "otp_code", length = 10)
	public String getOtpCode() {
		return this.otpCode;
	}

	public void setOtpCode(String otpCode) {
		this.otpCode = otpCode;
	}
	
	@Column(name = "gender", length = 2)
	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	@Column(name = "kelurahan", length = 50)
	public String getKelurahan() {
		return this.kelurahan;
	}

	public void setKelurahan(String kelurahan) {
		this.kelurahan = kelurahan;
	}

	@Column(name = "kecamatan", length = 50)
	public String getKecamatan() {
		return this.kecamatan;
	}

	public void setKecamatan(String kecamatan) {
		this.kecamatan = kecamatan;
	}

	@Column(name = "kota", length = 50)
	public String getKota() {
		return this.kota;
	}

	public void setKota(String kota) {
		this.kota = kota;
	}

	@Column(name = "zip_code", length = 10)
	public String getZipCode() {
		return this.zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "date_of_birth", length = 13)
	public Date getDateOfBirth() {
		return this.dateOfBirth;
	}

	public void setDateOfBirth(Date dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}

	@Column(name = "photo_self")
	public byte[] getPhotoSelf() {
		return this.photoSelf;
	}

	public void setPhotoSelf(byte[] photoSelf) {
		this.photoSelf = photoSelf;
	}

	@Column(name = "place_of_birth", length = 100)
	public String getPlaceOfBirth() {
		return this.placeOfBirth;
	}

	public void setPlaceOfBirth(String placeOfBirth) {
		this.placeOfBirth = placeOfBirth;
	}

	@Column(name = "provinsi", length = 50)
	public String getProvinsi() {
		return this.provinsi;
	}

	public void setProvinsi(String provinsi) {
		this.provinsi = provinsi;
	}

	@Column(name = "email", length = 80)
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = "id_no", length = 50)
	public String getIdNo() {
		return this.idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	@Column(name = "phone", length = 20)
	public String getPhone() {
		return this.phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	@Column(name = "address", length = 500)
	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@Column(name = "photo_id")
	public byte[] getPhotoId() {
		return this.photoId;
	}

	public void setPhotoId(byte[] photoId) {
		this.photoId = photoId;
	}

	@Column(name = "full_name", length = 80)
	public String getFullName() {
		return this.fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	@Column(name = "region", length = 50)
	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	@Column(name = "office", length = 50)
	public String getOffice() {
		return office;
	}

	public void setOffice(String office) {
		this.office = office;
	}

	@Column(name = "ref_number", length = 50)
	public String getRefNumber() {
		return refNumber;
	}

	public void setRefNumber(String refNumber) {
		this.refNumber = refNumber;
	}

	@Column(name = "business_line", length = 50)
	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_user_type")
	public MsLov getLovUserType() {
		return lovUserType;
	}

	public void setLovUserType(MsLov lovUserType) {
		this.lovUserType = lovUserType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_office")
	public MsOffice getMsOffice() {
		return msOffice;
	}

	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_business_line")
	public MsBusinessLine getMsBusinessLine() {
		return msBusinessLine;
	}

	public void setMsBusinessLine(MsBusinessLine msBusinessLine) {
		this.msBusinessLine = msBusinessLine;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_register_attempt")
	public Date getLastRegisterAttempt() {
		return this.lastRegisterAttempt;
	}

	public void setLastRegisterAttempt(Date lastRegisterAttempt) {
		this.lastRegisterAttempt = lastRegisterAttempt;
	}

	@Column(name = "daily_register_attempt_amount")
	public Short getDailyRegisterAttemptAmount() {
		return this.dailyRegisterAttemptAmount;
	}

	public void setDailyRegisterAttemptAmount(Short dailyRegisterAttemptAmount) {
		this.dailyRegisterAttemptAmount = dailyRegisterAttemptAmount;
	}

	@Column(name = "is_redirect_url", length = 1)
	public String getIsRedirectUrl() {
		return isRedirectUrl;
	}

	public void setIsRedirectUrl(String isRedirectUrl) {
		this.isRedirectUrl = isRedirectUrl;
	}
	
	@Column(name = "is_embed", length = 1)
	public String getIsEmbed() {
		return isEmbed;
	}

	public void setIsEmbed(String isEmbed) {
		this.isEmbed = isEmbed;
	}
	
	@Column(name = "use_sync_privy_verif", length = 1)
	public String getUseSyncPrivyVerif() {
		return this.useSyncPrivyVerif;
	}

	public void setUseSyncPrivyVerif(String useSyncPrivyVerif) {
		this.useSyncPrivyVerif = useSyncPrivyVerif;
	}

	@Column(name = "notification_attempt_num")
	public Short getNotificationAttemptNum() {
		return this.notificationAttemptNum;
	}

	public void setNotificationAttemptNum(Short notificationAttemptNum) {
		this.notificationAttemptNum = notificationAttemptNum;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "notification_attempt_date")
	public Date getNotificationAttemptDate() {
		return this.notificationAttemptDate;
	}

	public void setNotificationAttemptDate(Date notificationAttemptDate) {
		this.notificationAttemptDate = notificationAttemptDate;
	}
	
}
