package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.mail.MessagingException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.NoSuchMessageException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.InvitationLinkLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.NotificationLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.TrInvitationLinkHistory;
import com.adins.esign.model.TrMessageDeliveryReport;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrUserDataAccessLog;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiRequest;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiResponse;
import com.adins.esign.model.custom.ChangeEmailPhoneRequestBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.GenerateInvLinkRequest;
import com.adins.esign.model.custom.GenerateInvLinkResponse;
import com.adins.esign.model.custom.GenerateInvLinkUserBean;
import com.adins.esign.model.custom.NikPhoneEmailValidationBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.SendInvitationLinkNotifBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.GenerateInvitationLinkForExpiredCertRequest;
import com.adins.esign.webservices.model.GenerateInvitationLinkForExpiredCertResponse;
import com.adins.esign.webservices.model.RegenerateInvitationRequest;
import com.adins.esign.webservices.model.RegenerateInvitationResponse;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.esign.webservices.model.UpdateInvDataRequest;
import com.adins.esign.webservices.model.UpdateInvDataResponse;
import com.adins.esign.webservices.model.embed.GenerateInvitationLinkForExpiredCertEmbedRequest;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalRequest;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalResponse;
import com.adins.exceptions.DigisignException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.InvitationLinkException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.RegisterException;
import com.adins.exceptions.SendNotificationException;
import com.adins.exceptions.SendNotificationException.ReasonSendNotif;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.UserException;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.InvitationLinkException.ReasonInvitationLink;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.util.Tool;

@Component
public class GenericInvitationLinkLogic extends BaseLogic implements InvitationLinkLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericInvitationLinkLogic.class);
	
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic;
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private SmsLogic smsLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private WhatsAppLogic whatsAppLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private JatisSmsLogic jatisSmsLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	@Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;
	@Autowired private NotificationLogic notificationLogic;
	@Autowired private MessageDeliveryReportLogic messageDeliveryReportLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	
	private static final String MSG_MISMATCH_RECEIVER_DETAIL = "businesslogic.invitationlink.mismatchreceiverdetail";
	private static final String MSG_INVALID_RECEIVER_DETAIL = "businesslogic.invitationlink.invalidreceiverdetail";
	private static final String MSG_VENDORCODE_INVALID = "businesslogic.vendor.vendorcodeinvalid";
	private static final String MSG_EXISTING_PHONE = "businesslogic.invitationlink.existingphone";
	private static final String VFIRST_ERR28681 = "28681";
	private static final String VFIRST_ERR28682 = "28682";
	private static final String VFIRST_ERR408 = "408";
	private static final String PHONE = "Phone Num/No. Telp.";
	private static final String TENANT_NAME = "tenantName";
	private static final String TEMPLATE_INV_LINK_ACTIVE_DURATION = "invitation_link_active_duration";
	
	@Value("${esign.regex.phone}") private String regexPhone;
	@Value("${esign.regex.email}") private String regexEmail;
	@Value("${spring.mail.username}") private String fromEmailAddr;
	@Value("${regist.inv.url}") private String regInvLink;
	

	@Override
	public void deactivateInvitationLink(TrInvitationLink invitationLink, AuditContext audit) {
		if (null == invitationLink) {
			return;
		}
		invitationLink.setIsActive("0");
		invitationLink.setUsrUpd(audit.getCallerId());
		invitationLink.setDtmUpd(new Date());
		daoFactory.getInvitationLinkDao().updateInvitationLink(invitationLink);
	}

	@Override
	public String getDataUserInvitation(String receiverDetail, String vendorCode, AuditContext audit) {
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvLinkByReceiverDetailAndVendorCode(receiverDetail.toUpperCase(), vendorCode);
		if (null == invLink) {
			throw new InvitationLinkException(
					getMessage("businesslogic.invitationlink.invitationdatainvalid", null, audit),
					ReasonInvitationLink.INV_LINK_NOT_EXIST);
		}
		String nik = null;
		String nama = null;

		nik = invLink.getIdNo();
		nama = invLink.getFullName();
		
		if(StringUtils.isBlank(invLink.getIdNo())&& StringUtils.isNotBlank(invLink.getFullName())) {
			nik = "";
		} else if(StringUtils.isBlank(invLink.getFullName()) && StringUtils.isNotBlank(invLink.getIdNo())) {
			nama = "";
		} else if(StringUtils.isBlank(invLink.getFullName()) && StringUtils.isBlank(invLink.getIdNo())) {
			nik = "";
			nama = "";
		}
		
		String result = "(" + nik + ", " + nama  + ")";  
		LOG.info(result);
		return result;
	}

	@Override
	public boolean isInvitationLinkRegenerable(TrInvitationLink invLink, AuditContext audit) {
		if ("1".equals(invLink.getIsActive())) {
			return true;
		}
		
		String vendorCode = invLink.getMsVendor().getVendorCode();
		MsVendorRegisteredUser vendorUser = null;
		if (GlobalVal.INV_BY_EMAIL.equals(invLink.getInvitationBy())) {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(invLink.getReceiverDetail(), vendorCode);
		} else {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(invLink.getReceiverDetail(), vendorCode);
		}
		
		boolean userHasNotRegistered = null == vendorUser || "0".equals(vendorUser.getIsRegistered());
		return "0".equals(invLink.getIsActive()) && userHasNotRegistered;
	}

	@Override
	public boolean isInvitationLinkEditable(TrInvitationLink invLink, AuditContext audit) {
		if (!"1".equals(invLink.getIsActive())) {
			return false;
		}
		
		return "1".equals(invLink.getMsVendor().getEditAfterRegister());
	}
	
	@Override
	public GenerateInvLinkResponse generateAndSendRegInvLink(GenerateInvLinkRequest request, boolean isMenu, AuditContext audit) throws Exception {

		getHighestPriorityUnregisteredVendorCode(request, audit);

		return generateAndSendRegInvLinkV2(request, isMenu, audit);
	}
	
	private void getHighestPriorityUnregisteredVendorCode(GenerateInvLinkRequest req, AuditContext audit) {
		
		MsVendor vendor;
		if (StringUtils.isNotBlank(req.getPsreCode())) {
			vendor = vendorValidatorLogic.validateVendorOfTenant(req.getPsreCode(), req.getTenantCode(), true, audit);
		} else {
			vendor = vendorValidatorLogic.validateGetMainDefaultVendor(req.getTenantCode(), false, audit);
		}
		
		for (GenerateInvLinkUserBean bean : req.getUsers()) {
			AmMsuser user = daoFactory.getUserDao().getUserByIdNo(bean.getIdNo());

			if (null == user) {
				user = userValidatorLogic.validateGetUserByEmailv2(bean.getEmail(), false, audit);
			}

			if (null == user) {
				user = userValidatorLogic.validateGetUserByPhone(bean.getUserPhone(), false, audit);
			}

			List<String> excludedVendors = new ArrayList<>();
			if ("1".equals(vendor.getIsOperating())) {
				bean.setVendorCode(vendor.getVendorCode());
			} else if (null == user) {
				bean.setVendorCode(daoFactory.getVendorDao().getHighestPriorityVendorCodeAvailable(req.getTenantCode(),
						excludedVendors));
			} else {
				excludedVendors = daoFactory.getVendorRegisteredUserDao().getUserRegisteredVendorsByIdMsUser(user.getIdMsUser());
				bean.setVendorCode(daoFactory.getVendorDao().getHighestPriorityVendorCodeAvailable(req.getTenantCode(),
						excludedVendors));
			}

		}

	}
	
	@Override
	public GenerateInvLinkResponse generateAndSendRegInvLinkV2(GenerateInvLinkRequest request, boolean isMenu, AuditContext audit) throws Exception {

		MsTenant tenant = null;
		MsVendor vendor = null;
		MsLov roleType = null;
		GenerateInvLinkUserBean userBean = null;
		GenerateInvLinkResponse response = new GenerateInvLinkResponse();
		Status status = new Status();
		List<String> links = new ArrayList<>();

		if (isMenu){
			if (StringUtils.isBlank(request.getRoleType())) {
				throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {"Role Type"}, this.retrieveLocaleAudit(audit)), 
						ReasonUser.PARAM_INVALID);
			}

			roleType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_USER_TYPE, request.getRoleType());

			if (null == roleType){
				throw new UserException(messageSource.getMessage("businesslogic.user.invalidusertype", new Object[] {request.getRoleType()}, this.retrieveLocaleAudit(audit)),
						ReasonUser.PARAM_INVALID);
			}
		}

		try {
			tenant = daoFactory.getTenantDao().getTenantByCodeNewTrx(request.getTenantCode());
			MsBusinessLine businessLine = checkBusinessLine(request.getBusinessLineCode(), request.getBusinessLineName(), tenant, audit);
			MsRegion region = checkRegion(request.getRegionCode(), request.getRegionName(), tenant, audit);
			MsOffice office = checkOffice(request.getOfficeCode(), request.getOfficeName(), tenant, region, audit);
			
			Status checkStatus = this.checkPhoneEmailNik(request, tenant, true, audit);
			if (0 != checkStatus.getCode()) {
				String[] linksArray = new String[links.size()];
				links.toArray(linksArray);
				response.setStatus(checkStatus);
				response.setLinks(linksArray);
				return response;
			}

			boolean isUpd = false;
			for (GenerateInvLinkUserBean user : request.getUsers()) {
				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
				vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(user.getVendorCode());
				MsLov process = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_INV_LINK_REQ);
				auditTrail.setLovProcessType(process);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendor);
				auditTrail.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
				auditTrail.setDtmCrt(new Date());
				
				userBean = user;
				auditTrail.setEmail(userBean.getEmail());
				if (StringUtils.isNotBlank(userBean.getUserPhone())) {
					auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(userBean.getUserPhone()));
					auditTrail.setHashedPhoneNo( MssTool.getHashedString(userBean.getUserPhone()));
				}
				
				if (this.isNotExistingUserV2(userBean.getEmail(), userBean.getIdNo(), userBean.getUserPhone(), tenant, vendor, audit)) {
					NikPhoneEmailValidationBean valBean = new NikPhoneEmailValidationBean();
					valBean.setEmail(userBean.getEmail());
					valBean.setIpAddress(StringUtils.isBlank(request.getIpAddress()) ? audit.getCallerId() : request.getIpAddress());
					valBean.setNik(userBean.getIdNo());
					valBean.setPhone(userBean.getUserPhone());
					
					TrInvitationLink invLink = this.checkExistingNikEmailPhoneInvitationLinkV2(valBean, userBean.getVendorCode(), false, tenant.getTenantCode(), audit);

					if (null == invLink) {
						if (StringUtils.isNotBlank(userBean.getEmail())) {
							invLink = this.insertInvitationLink(userBean, GlobalVal.INV_BY_EMAIL, userBean.getEmail(),
									tenant, office, businessLine, request.getReferenceNo(), roleType, audit);
						} else {
							invLink = this.insertInvitationLink(userBean, GlobalVal.INV_BY_SMS, userBean.getUserPhone(),
									tenant, office, businessLine, request.getReferenceNo(), roleType, audit);
						}
					} else {
						this.updateInvitaitonLink(userBean, invLink, office, businessLine, request, tenant, roleType, audit);
						isUpd = true;
					}
					
					SendInvitationLinkNotifBean sendNotifBean = new SendInvitationLinkNotifBean();
					sendNotifBean.setInvLink(invLink);
					sendNotifBean.setLinks(links);
					sendNotifBean.setTenant(tenant);
					sendNotifBean.setUserName(userBean.getUserName());
					sendNotifBean.setOffice(office);
					sendNotifBean.setBusinessLine(businessLine);
					sendNotifBean.setRefNo(request.getReferenceNo());
					
					NotificationSendingPoint sendPoint = isMenu ? NotificationSendingPoint.GEN_INV_MENU :  NotificationSendingPoint.GEN_INV;
					MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, sendPoint.getLovCode());
					auditTrail.setLovSendingPoint(sendingPoint);
					auditTrail.setNotes(invLink.getInvitationCode());
					auditTrail.setResultStatus("1");
					auditTrail.setTrInvitationLink(invLink);

					if (!isMenu) {
						String emailService = invLink.getInvitationBy().equals(GlobalVal.INV_BY_EMAIL) ? "0" : "1";
						NotificationType type = tenantLogic.getNotificationType(tenant, sendPoint, emailService);
						MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, sendPoint.getLovCode());
						String phoneNumber = StringUtils.isNumeric(invLink.getReceiverDetail()) ? invLink.getReceiverDetail() : invLink.getPhone();
						
						if (type != NotificationType.EMAIL){
							validateNotificationLimitByPeriod(sendingPointLov, tenant, phoneNumber, audit);
							validateNotificationDailyAttempt(invLink, tenant, audit);
						}
					}

					sendInvitationLinkNotif(sendNotifBean, isUpd, sendPoint, auditTrail, vendor, audit);
					links = sendNotifBean.getLinks();
				} else {
					links.add(StringUtils.EMPTY);
				}
			}

			status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
			
			if (links.size() == 1 && StringUtils.isBlank(links.get(0))) {
				status.setMessage(messageSource.getMessage("businesslogic.user.alreadyregistered", null, this.retrieveLocaleAudit(audit)));
			}

			response.setStatus(status);
			String[] linksArray = new String[links.size()];
			links.toArray(linksArray);
			response.setLinks(linksArray);

		} catch (SendNotificationException e) {
			status.setCode(e.getErrorCode());
			status.setMessage(e.getLocalizedMessage());
			response.setStatus(status);
		} catch (NullPointerException e) {
			// Message NPE biasanya kosong, ada message sendiri jika message NPE kosong
			String npeMessage = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage()
					: "NullPointerException caught";
			String custName = (null == userBean) ? null : userBean.getUserName();
			String[] linksArray = new String[links.size()];
			links.toArray(linksArray);

			insertGenerateInvErrorHistory(tenant, custName, GlobalVal.ERROR_TYPE_ERROR, npeMessage, vendor, audit);
			status.setCode(StatusCode.UNKNOWN);
			status.setMessage(npeMessage);
			response.setStatus(status);
			response.setLinks(linksArray);
			LOG.error(GlobalVal.CONST_ERROR_MESSAGE_GENERATE_AND_SEND_REG_INV_LINK_ERROR, e);
		} catch (Exception e) {
			String custName = (null == userBean) ? null : userBean.getUserName();
			String[] linksArray = new String[links.size()];
			links.toArray(linksArray);
			insertGenerateInvErrorHistory(tenant, custName, GlobalVal.ERROR_TYPE_ERROR, e.getLocalizedMessage(), vendor, audit);
			LOG.error(GlobalVal.CONST_ERROR_MESSAGE_GENERATE_AND_SEND_REG_INV_LINK_ERROR, e);
			throw new InvitationLinkException(getMessage("businesslogic.invitationlink.errorhappened", null, audit), ReasonInvitationLink.UNKOWN);
		}

		return response;

	}

    private void validateNotificationDailyAttempt(TrInvitationLink invLink, MsTenant tenant, AuditContext audit) {
        int dailyLimit = tenantSettingsLogic.getSettingValue(tenant, "MAX_NOTIF_SENDING_POINT_DAILY", 0);
        if (dailyLimit == 0) {
            String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode("MAX_NOTIF_SENDING_POINT_DAILY");
            try {
                dailyLimit = Integer.parseInt(gsValue);
            } catch (Exception e) {
                dailyLimit = 0;
            }
        }
        if (dailyLimit == 0) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitnotset", null, audit), ReasonSendNotif.NOTIF_LIMIT_NOT_SET);
        }

        if (invLink.getNotificationAttemptDate() == null || invLink.getNotificationAttemptNum() == null || !DateUtils.isSameDay(invLink.getNotificationAttemptDate(), new Date())) {
            invLink.setNotificationAttemptNum((short) 0);
        }

		if (invLink.getNotificationAttemptNum() >= dailyLimit) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitreached", null, audit), ReasonSendNotif.NOTIF_DAILY_LIMIT_REACHED);
        }

        invLink.setNotificationAttemptNum((short) (invLink.getNotificationAttemptNum() + 1));
        invLink.setNotificationAttemptDate(new Date());

        invLink.setUsrUpd(audit.getCallerId());
        invLink.setDtmUpd(new Date());
        daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
    }

	private void validateNotificationLimitByPeriod(MsLov sendingPoint, MsTenant tenant, String phoneNumber, AuditContext audit) {
		String timePeriodLimit = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_LIMIT_NOTIF_TIME_PERIOD, "0");
		if ("0".equals(timePeriodLimit)) {
			timePeriodLimit = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_LIMIT_NOTIF_TIME_PERIOD);
		}
		int periodLimitMinutes = Integer.parseInt(timePeriodLimit);

		String notificationLimit = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_LIMIT_SEND_NOTIF_BY_PERIOD, "0");
		if ("0".equals(notificationLimit)) {
			notificationLimit = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_LIMIT_SEND_NOTIF_BY_PERIOD);
		}
		int notificationLimitInt = Integer.parseInt(notificationLimit);

		Date periodStartTime = Tool.addMinuteFromNow(-periodLimitMinutes);

		int notificationCount = 0;

		notificationCount = daoFactory.getMessageDeliveryReportDao().countNotificationByPeriod(null, sendingPoint, periodStartTime, phoneNumber);
		

		if (notificationCount >= notificationLimitInt) {
			throw new SendNotificationException(
				getMessage("businesslogic.global.limitnotification", new Object[] { periodLimitMinutes }, audit), ReasonSendNotif.NOTIF_PERIOD_LIMIT_REACHED);
		}
    }
	
	private MsOffice checkOffice(String officeCode, String officeName, MsTenant tenant, MsRegion region, AuditContext audit) {
		MsOffice officeBean = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(officeCode, tenant.getTenantCode()) ;
		if(null == officeBean && StringUtils.isNotBlank(officeCode)) {
			officeBean = new MsOffice();
			officeBean.setOfficeCode(StringUtils.upperCase(officeCode));
			officeBean.setIsActive("1");
			officeBean.setOfficeName(StringUtils.upperCase(officeName));
			officeBean.setMsTenant(tenant);
			officeBean.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			officeBean.setDtmCrt(new Date());
			officeBean.setMsRegion(region);
			daoFactory.getOfficeDao().insertOffice(officeBean);
		} else if (null != officeBean && null == officeBean.getMsRegion()) {
			officeBean.setMsRegion(region);
			daoFactory.getOfficeDao().updateOffice(officeBean);
		}
		
		return officeBean;
	}
	
	private MsBusinessLine checkBusinessLine(String businessLineCode, String businessLineName, MsTenant tenant, AuditContext audit) {
		MsBusinessLine businessLine = daoFactory.getBusinessLineDao().getBusinessLineByCodeAndTenant(businessLineCode, tenant.getTenantCode());
		if (null == businessLine && StringUtils.isNotBlank(businessLineCode)) {
			businessLine = new MsBusinessLine();
			businessLine.setDtmCrt(new Date());
			businessLine.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			businessLine.setBusinessLineCode(StringUtils.upperCase(businessLineCode));
			businessLine.setBusinessLineName(StringUtils.upperCase(businessLineName));
			businessLine.setMsTenant(tenant);
			daoFactory.getBusinessLineDao().insertBusinessLine(businessLine);
		}
		
		return businessLine;
	}
	
	private MsRegion checkRegion(String regionCode, String regionName, MsTenant tenant, AuditContext audit) {
		MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenant(regionCode, tenant.getTenantCode());
		if (null == region && StringUtils.isNotBlank(regionCode)) {
			region = new MsRegion();
			region.setDtmCrt(new Date());
			region.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			region.setMsTenant(tenant);
			region.setRegionCode(StringUtils.upperCase(regionCode));
			region.setRegionName(StringUtils.upperCase(regionName));
			daoFactory.getRegionDao().insertRegion(region);
		}
		
		return region;
	}

	private Status checkPhoneEmailNik(GenerateInvLinkRequest request, MsTenant tenant, boolean isV2,
			AuditContext audit) {
		Status status = new Status();
		Map<Integer, String> errors = new HashMap<>();

		AmGeneralsetting gsPhone = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue()
				: regexPhone;
		AmGeneralsetting gsEmail = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT);
		String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue()
				: regexEmail;
		List<String> emails = new ArrayList<>();
		List<String> phones = new ArrayList<>();
		List<String> niks = new ArrayList<>();

		int j = 0;
		for (GenerateInvLinkUserBean bean : request.getUsers()) {
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(bean.getVendorCode());
			try {
				if (StringUtils.isEmpty(bean.getVendorCode()) && isV2) {
					throw new VendorException(messageSource.getMessage("businesslogic.user.vendorcannotbeempty", null,
							this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_EMPTY);
				}

				if (null == vendor && isV2) {
					throw new VendorException(messageSource.getMessage(MSG_VENDORCODE_INVALID,
							new Object[] { bean.getVendorCode() }, this.retrieveLocaleAudit(audit)),
							ReasonVendor.VENDOR_CODE_INVALID);
				}
				if ("0".equals(tenant.getEmailService()) && StringUtils.isBlank(bean.getEmail())) {
					throw new UserException(messageSource.getMessage("businesslogic.document.emptyemail", null,
							this.retrieveLocaleAudit(audit)), ReasonUser.EMAIL_EMPTY);
				}
				this.checkDuplicateNikInvLink(bean.getIdNo(), niks, audit);
				String resultEmail = this.checkDuplicateEmailInvLink(bean.getEmail(), emails, emailRegex, audit);
				if (StringUtils.isNotBlank(resultEmail)) {
					errors.put(j, resultEmail);
				}

				String resultPhone = this.checkDuplicatePhoneInvLink(bean.getUserPhone(), phones, phoneNoRegex,
						bean.getEmail(), audit);
				if (StringUtils.isNotBlank(resultPhone)) {
					errors.put(j, resultPhone);
				}

				if (isV2) {
					this.checkExistingNikEmailPhoneV2(bean.getUserPhone(), bean.getEmail(), bean.getIdNo(),
							bean.getVendorCode(), audit);
				} else {
					this.checkExistingNikEmailPhone(bean.getUserPhone(), bean.getEmail(), bean.getIdNo(), audit);
				}

			} catch (UserException | InvitationLinkException e) {
				
				String userName = null;
				if (StringUtils.isNotBlank(bean.getUserName())) {
					userName = bean.getUserName();
				} else {
					userName = StringUtils.isBlank(bean.getEmail()) ? bean.getUserPhone() :bean.getEmail();
				}
				
				audit.setCallerId(userName);
				insertGenerateInvErrorHistory(tenant, userName, GlobalVal.ERROR_TYPE_REJECT, e.getLocalizedMessage(),
						vendor, audit);
				status.setCode(e.getErrorCode());
				status.setMessage(e.getLocalizedMessage());
				LOG.error(GlobalVal.CONST_ERROR_MESSAGE_GENERATE_AND_SEND_REG_INV_LINK_ERROR, e);
				return status;
			}

			j++;
		}

		List<UserBean> users = new LinkedList<>(Arrays.asList(request.getUsers()));
		for (int i = users.size() - 1; i >= 0; i--) {
			if (errors.containsKey(i)) {
				users.remove(i);
			}
		}

		if (users.isEmpty()) {
			status.setCode(9999);
			StringBuilder msg = new StringBuilder();
			for (int i = 0; i < errors.size(); i++) {
				if (StringUtils.isNotBlank(msg.toString())) {
					msg.append(" | " + errors.get(i));
				} else {
					msg.append(errors.get(i));
				}
			}
			status.setMessage(msg.toString());
			return status;
		}

		request.setUsers(users.toArray(new GenerateInvLinkUserBean[0]));

		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		return status;
	}
	
	private boolean isNotExistingUserV2(String email, String idNo, String phone, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendor.getVendorCode());
		boolean isECertExpired = false;
		boolean byPassExpiryValidation = false;
		
		if (vru != null) {
			isECertExpired = userValidatorLogic.isCertifExpiredForRegister(vru, audit);
			byPassExpiryValidation = StringUtils.isBlank(vru.getVendorRegistrationId());

			// Kalau email exists in vendor user, cert aktif, dan user bukan autosign
			if (!isECertExpired && !byPassExpiryValidation) {
				AmMsuser user = daoFactory.getUserDao().getUserByIdMsUserNewTrx(vru.getAmMsuser().getIdMsUser());
				insertUserofTenant(user, tenant, audit);
				return false;
			}
		}

		if (StringUtils.isNotBlank(phone)) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone,
					vendor.getVendorCode());
		}
		
		if (null != vru) {
			byPassExpiryValidation = StringUtils.isBlank(vru.getVendorRegistrationId());
			isECertExpired = userValidatorLogic.isCertifExpiredForRegister(vru, audit);

			// Kalau email exists in vendor user, cert aktif, dan user bukan autosign
			if (!isECertExpired && !byPassExpiryValidation) {
				AmMsuser user = getUserNewTrx(idNo, phone, email, vendor.getVendorCode());
				insertUserofTenant(user, tenant, audit);
				return false;
			}
		}

		// Cert expired OR bypass expired validation
		// Email & phone belum ada di ms_vendor_registered_user
		
		Status stat = userValidatorLogic.validateNikPhoneEmailForRegisterDefaultVendorStatus(idNo, phone, email, vendor, audit);
		String expectedMessage = getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_USER_REGISTERED, null, audit);

		if (stat.getCode() != 0 && StringUtils.isNotBlank(stat.getMessage())) {
			AmMsuser user = getUserNewTrx(idNo, phone, email, vendor.getVendorCode());
			insertUserofTenant(user, tenant, audit);
			// Return true jika cert expired dan validasi message = "Anda sudah terdaftar" OR jika user autosign dan message = "Anda sudah terdaftar"
			return (byPassExpiryValidation || isECertExpired) && expectedMessage.equals(stat.getMessage());
		}
		
		return true;
	}
	
	private AmMsuser getUserNewTrx(String idNo, String phone, String email, String vendorCode) {
		MsVendorRegisteredUser vru = null;
		
		if (StringUtils.isNotBlank(email)) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendorCode);
		}
		
		if (null == vru) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(idNo, vendorCode);
		}
		
		if (null == vru) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		}
		
		return daoFactory.getUserDao().getUserByIdMsUserNewTrx(vru.getAmMsuser().getIdMsUser());
	}
	
	private void insertUserofTenant(AmMsuser user, MsTenant tenant, AuditContext audit) {
		MsUseroftenant uot = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		
		if (null == uot) {
			uot = new MsUseroftenant();
			uot.setAmMsuser(user);
			uot.setMsTenant(tenant);
			uot.setDtmCrt(new Date());
			uot.setUsrCrt(audit.getCallerId());
			
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(uot);
		}
	}
	
	private TrInvitationLink checkExistingNikEmailPhoneInvitationLinkV2(NikPhoneEmailValidationBean valBean, String vendorCode, boolean isExternal,
			String tenantCode, AuditContext audit) {
		String nik = valBean.getNik();
		String phone = valBean.getPhone();
		String email = valBean.getEmail();
		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_GEN_INV_LINK);
		TrInvitationLink existingInvitation = daoFactory.getInvitationLinkDao().getInvitationByIdNoAndVendorCode(nik, vendorCode);
		MsVendorRegisteredUser vruNik = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(nik, vendorCode);
		if (null != existingInvitation) {
			// validate invitation with default vendor
			TrInvitationLink invValPhone = daoFactory.getInvitationLinkDao().getInvitationByPhoneAndVendorCode(phone, vendorCode);
			if (null != invValPhone && invValPhone != existingInvitation) {
				if (null != vruNik) {
					String hashedPhone = MssTool.getHashedString(phone);
					String phoneInExisting = GlobalVal.INV_BY_EMAIL.equals(existingInvitation.getInvitationBy()) ? existingInvitation.getPhone() : existingInvitation.getReceiverDetail();
					String hashedPhoneInExisting = MssTool.getHashedString(phoneInExisting);
					if (vruNik.getHashedSignerRegisteredPhone().equals(hashedPhone)) {
						TrInvitationLinkHistory history = insertInvitationLinkHistory(existingInvitation, valBean.getIpAddress(), false, false, false,actionType, audit);
						addInvitatioLinkHistoryToExistingAuditTrail(history, existingInvitation, audit);
						daoFactory.getInvitationLinkDao().deleteInvitationLink(existingInvitation);
						
						existingInvitation = invValPhone;
					} else if (vruNik.getHashedSignerRegisteredPhone().equals(hashedPhoneInExisting)) {
						throw new InvitationLinkException(messageSource.getMessage(MSG_EXISTING_PHONE, new Object[] {phone}, this.retrieveLocaleAudit(audit))
								, ReasonInvitationLink.INVALID_PHONE_NO);
					}
					
				} else {
					throw new InvitationLinkException(messageSource.getMessage(MSG_EXISTING_PHONE, new Object[] {phone}, this.retrieveLocaleAudit(audit))
							, ReasonInvitationLink.INVALID_PHONE_NO);
				}
			}
			
			TrInvitationLink invValEmail = daoFactory.getInvitationLinkDao().getInvitationByEmailAndVendorCode(email, vendorCode);
			if (null != invValEmail && invValEmail != existingInvitation) {
				if (null != vruNik) {
					if (vruNik.getSignerRegisteredEmail().equals(email)) {
						TrInvitationLinkHistory history = insertInvitationLinkHistory(existingInvitation, valBean.getIpAddress(), false, false, false,actionType, audit);
						addInvitatioLinkHistoryToExistingAuditTrail(history, existingInvitation, audit);
						daoFactory.getInvitationLinkDao().deleteInvitationLink(existingInvitation);
						
						existingInvitation = invValEmail;
					} else if (vruNik.getSignerRegisteredEmail().equals(invValEmail.getReceiverDetail())) {
						throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL, new Object[] {email}, this.retrieveLocaleAudit(audit))
								, ReasonInvitationLink.INVALID_EMAIL);
					}
					
				} else {
					throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL, new Object[] {email}, this.retrieveLocaleAudit(audit))
							, ReasonInvitationLink.INVALID_EMAIL);
				}
			}
			
			checkOtherVendorInvitationLink(phone, email, nik, vendorCode, audit);
			
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendorTenantByCode(tenantCode, vendorCode);
			
			if (null != existingInvitation && !existingInvitation.getIsActive().equals("1")) {
				if(!"1".equals(vot.getAllowRegenerateInvLink())) {
					throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.cannotregeneratelinkexisted", new Object[] {existingInvitation.getMsVendor().getVendorCode()}, this.retrieveLocaleAudit(audit)),
							ReasonInvitationLink.CANNOT_REGENERATE);
				}
					
				TrInvitationLinkHistory history = insertInvitationLinkHistory(existingInvitation, valBean.getIpAddress(), false, false, false,actionType, audit);
				addInvitatioLinkHistoryToExistingAuditTrail(history, existingInvitation, audit);
				daoFactory.getInvitationLinkDao().deleteInvitationLink(existingInvitation);
				
				return null;
			}
			
			return existingInvitation;
		}
		
		existingInvitation = daoFactory.getInvitationLinkDao().getInvitationByPhoneAndVendorCode(phone, vendorCode);
		MsVendorRegisteredUser vruPhone = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		if (null != existingInvitation) {
			
			TrInvitationLink invValEmail = daoFactory.getInvitationLinkDao().getInvitationByEmailAndVendorCode(email, vendorCode);
			if (null != invValEmail && invValEmail != existingInvitation) {
				if (null != vruPhone) {
					if (vruPhone.getSignerRegisteredEmail().equals(email)) {
						TrInvitationLinkHistory history = insertInvitationLinkHistory(existingInvitation, valBean.getIpAddress(), false, false, false,actionType, audit);
						addInvitatioLinkHistoryToExistingAuditTrail(history, existingInvitation, audit);
						daoFactory.getInvitationLinkDao().deleteInvitationLink(existingInvitation);
						
						existingInvitation = invValEmail;
					} else if (vruPhone.getSignerRegisteredEmail().equals(invValEmail.getReceiverDetail())) {
						throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL, new Object[] {email}, this.retrieveLocaleAudit(audit))
								, ReasonInvitationLink.INVALID_EMAIL);
					}
					
				} else {
					throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL, new Object[] {email}, this.retrieveLocaleAudit(audit))
							, ReasonInvitationLink.INVALID_EMAIL);
				}
			}
			
			checkOtherVendorInvitationLink(phone, email, nik, vendorCode, audit);
			
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendorTenantByCode(tenantCode, vendorCode);
			
			if (!existingInvitation.getIsActive().equals("1") && !isExternal) {
				if(!"1".equals(vot.getAllowRegenerateInvLink())) {
					throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.cannotregeneratelinkexisted", new Object[] {existingInvitation.getMsVendor().getVendorCode()}, this.retrieveLocaleAudit(audit)),
							ReasonInvitationLink.CANNOT_REGENERATE);
				}
					
				TrInvitationLinkHistory history = insertInvitationLinkHistory(existingInvitation, valBean.getIpAddress(), false, false, false,actionType, audit);
				addInvitatioLinkHistoryToExistingAuditTrail(history, existingInvitation, audit);
				daoFactory.getInvitationLinkDao().deleteInvitationLink(existingInvitation);
				
				return null;
			}
			
			return existingInvitation;
		}
		
		existingInvitation = daoFactory.getInvitationLinkDao().getInvitationByEmailAndVendorCode(email, vendorCode);
		if (null != existingInvitation) {
			checkOtherVendorInvitationLink(phone, email, nik, vendorCode, audit);
			
			if (!existingInvitation.getIsActive().equals("1") && !isExternal) {
				if (!existingInvitation.getIsActive().equals("1")) {
					TrInvitationLinkHistory history = insertInvitationLinkHistory(existingInvitation, valBean.getIpAddress(), false, false, false,actionType, audit);
					addInvitatioLinkHistoryToExistingAuditTrail(history, existingInvitation, audit);
					daoFactory.getInvitationLinkDao().deleteInvitationLink(existingInvitation);
				}
				
				return null;
			}
			
			return existingInvitation;
		}
		
		checkOtherVendorInvitationLink(phone, email, nik, vendorCode, audit);
		
		return null;
	}
	
	private void addInvitatioLinkHistoryToExistingAuditTrail(TrInvitationLinkHistory history, TrInvitationLink invLink, AuditContext audit) {
		List<TrSigningProcessAuditTrail> auditTrails = daoFactory.getSigningProcessAuditTrailDao().getListAuditTrailByIdInvitationLink(invLink.getIdInvitationLink());
		if (null != auditTrails && !auditTrails.isEmpty()) {
			for (TrSigningProcessAuditTrail auditTrail : auditTrails) {
				auditTrail.setTrInvitationLink(null);
				auditTrail.setTrInvitationLinkHistory(history);
				auditTrail.setUsrUpd(audit.getCallerId());
				auditTrail.setDtmUpd(new Date());
				daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrail(auditTrail);
			}
		}
	}
	
	private TrInvitationLink insertInvitationLink(GenerateInvLinkUserBean bean, String invBy, String receiverDetail,
			MsTenant tenant, MsOffice office, MsBusinessLine businessLine, String refNo, MsLov roleType, AuditContext audit) throws ParseException {
		Date now = new Date();
		TrInvitationLink invLink = new TrInvitationLink();
		invLink.setInvitationBy(invBy);
		invLink.setReceiverDetail(StringUtils.upperCase(receiverDetail));
		invLink.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		invLink.setDtmCrt(now);
		invLink.setIsActive("1");
		invLink.setMsTenant(tenant);
		invLink.setGender(StringUtils.upperCase(bean.getUserGender()));
		invLink.setKelurahan(StringUtils.upperCase(bean.getKelurahan()));
		invLink.setKecamatan(StringUtils.upperCase(bean.getKecamatan()));
		invLink.setKota(StringUtils.upperCase(bean.getKota()));
		invLink.setZipCode(StringUtils.upperCase(bean.getZipcode()));
		invLink.setPlaceOfBirth(StringUtils.upperCase(bean.getUserPob()));
		invLink.setFullName(bean.getUserName());
		invLink.setRegion(StringUtils.upperCase(bean.getRegion()));
		invLink.setOffice(StringUtils.upperCase(bean.getOffice()));
		invLink.setRefNumber(StringUtils.upperCase(bean.getTaskNo()));
		invLink.setBusinessLine(StringUtils.upperCase(bean.getBusinessLine()));
		invLink.setMsOffice(office);
		invLink.setMsBusinessLine(businessLine);
		invLink.setRefNumber(StringUtils.upperCase(refNo));

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(bean.getVendorCode());
		if (null == vendor) {
			List<MsVendoroftenant> vots = daoFactory.getVendorDao()
					.getListVendoroftenantByTenantCode(tenant.getTenantCode());
			if (vots.size() == 1) {
				invLink.setMsVendor(vots.get(0).getMsVendor());
			} else {
				for (MsVendoroftenant vot : vots) {
					TrInvitationLink exist = daoFactory.getInvitationLinkDao()
							.getInvitationLinkByRecieverDetailAndIdMsVendor(invLink.getReceiverDetail(),
									vot.getMsVendor().getIdMsVendor());

					if (null == exist) {
						invLink.setMsVendor(vot.getMsVendor());
					}
				}
			}
		} else {
			invLink.setMsVendor(vendor);
		}

		if (null != roleType){
			invLink.setLovUserType(roleType);
		}

		if (StringUtils.isNotBlank(bean.getUserDob())) {
			// Set lenient false supaya throw error jika format tidak sesuai
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.setLenient(false);
			invLink.setDateOfBirth(sdf.parse(bean.getUserDob()));
		}

		invLink.setProvinsi(StringUtils.upperCase(bean.getProvinsi()));

		if (StringUtils.isNotBlank(bean.getSelfPhoto())) {
			byte[] selfPhotoByteArray = MssTool.imageStringToByteArray(bean.getSelfPhoto());
			String key = cloudStorageLogic.storeRegistrationSelfie(bean.getIdNo(), selfPhotoByteArray);

			invLink.setPhotoSelf(key.getBytes());
		} else {
			invLink.setPhotoSelf(null);
		}

		if (StringUtils.isNotBlank(bean.getIdPhoto())) {
			byte[] idPhotoByteArray = MssTool.imageStringToByteArray(bean.getIdPhoto());
			String key = cloudStorageLogic.storeRegistrationKtp(bean.getIdNo(), idPhotoByteArray);

			invLink.setPhotoId(key.getBytes());
		} else {
			invLink.setPhotoId(null);
		}

		invLink.setIdNo(bean.getIdNo());
		invLink.setAddress(StringUtils.upperCase(bean.getUserAddress()));
		invLink.setPhone(bean.getUserPhone());
		invLink.setEmail(bean.getEmail());
		if (invLink.getInvitationBy().equalsIgnoreCase(GlobalVal.INV_BY_EMAIL)) {
			invLink.setEmail(StringUtils.upperCase(receiverDetail));
		} else {
			invLink.setPhone(receiverDetail);
		}

		invLink.setInvitationCode(generateInvitationCode());

		boolean useSyncPrivyVerif = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF);
		invLink.setUseSyncPrivyVerif(useSyncPrivyVerif ? "1" : "0");

		daoFactory.getInvitationLinkDao().insertInvitationLink(invLink);
		LOG.info("Invitation Link with code {} and receiver detail {} inserted", invLink.getInvitationCode(), invLink.getReceiverDetail());

		return invLink;
	}

	private void updateInvitaitonLink(GenerateInvLinkUserBean bean, TrInvitationLink invLink, MsOffice office, MsBusinessLine businessLine, GenerateInvLinkRequest request, MsTenant tenant, MsLov roleType, AuditContext audit)
			throws ParseException {
		boolean isEmailChanged = (StringUtils.isBlank(invLink.getEmail()) && StringUtils.isNotBlank(bean.getEmail())) || (StringUtils.isNotBlank(bean.getEmail()) && !invLink.getEmail().equals(bean.getEmail()));
		boolean isPhoneChanged = !invLink.getPhone().equals(bean.getUserPhone());
		boolean isNikChanged = !invLink.getIdNo().equals(bean.getIdNo());
		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_UPDATE_INV_LINK);
		if (isEmailChanged || isPhoneChanged || isNikChanged) {
			insertInvitationLinkHistory(invLink, request.getIpAddress(), isEmailChanged, isNikChanged, isPhoneChanged,actionType, audit);
		}
		
		invLink.setMsTenant(tenant);
		invLink.setGender(StringUtils.upperCase(bean.getUserGender()));
		invLink.setKelurahan(StringUtils.upperCase(bean.getKelurahan()));
		invLink.setKecamatan(StringUtils.upperCase(bean.getKecamatan()));
		invLink.setKota(StringUtils.upperCase(bean.getKota()));
		invLink.setZipCode(StringUtils.upperCase(bean.getZipcode()));
		invLink.setPlaceOfBirth(StringUtils.upperCase(bean.getUserPob()));
		invLink.setFullName(bean.getUserName());

		if (StringUtils.isNotBlank(bean.getUserDob())) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			invLink.setDateOfBirth(sdf.parse(bean.getUserDob()));
		}

		if (null != roleType){
			invLink.setLovUserType(roleType);
		}

		invLink.setProvinsi(StringUtils.upperCase(bean.getProvinsi()));

		if (StringUtils.isNotBlank(bean.getSelfPhoto())) {
			byte[] selfPhotoByteArray = MssTool.imageStringToByteArray(bean.getSelfPhoto());
			String key = cloudStorageLogic.storeRegistrationSelfie(bean.getIdNo(), selfPhotoByteArray);

			invLink.setPhotoSelf(key.getBytes());
		} else {
			invLink.setPhotoSelf(null);
		}

		if (StringUtils.isNotBlank(bean.getIdPhoto())) {
			byte[] idPhotoByteArray = MssTool.imageStringToByteArray(bean.getIdPhoto());
			String key = cloudStorageLogic.storeRegistrationKtp(bean.getIdNo(), idPhotoByteArray);

			invLink.setPhotoId(key.getBytes());
		} else {
			invLink.setPhotoId(null);
		}

		invLink.setIdNo(bean.getIdNo());
		invLink.setAddress(StringUtils.upperCase(bean.getUserAddress()));
		invLink.setPhone(bean.getUserPhone());
		invLink.setRegion(StringUtils.upperCase(bean.getRegion()));
		invLink.setOffice(StringUtils.upperCase(bean.getOffice()));
		invLink.setBusinessLine(StringUtils.upperCase(bean.getBusinessLine()));
		invLink.setRefNumber(StringUtils.upperCase(bean.getTaskNo()));
		invLink.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));
		invLink.setDtmUpd(new Date());
		invLink.setMsOffice(office);
		invLink.setMsBusinessLine(businessLine);
		invLink.setRefNumber(request.getReferenceNo());

		if (StringUtils.isNotBlank(bean.getEmail()) && invLink.getInvitationBy().equals(GlobalVal.INV_BY_EMAIL)) {
			invLink.setInvitationBy(GlobalVal.INV_BY_EMAIL);
			invLink.setReceiverDetail(StringUtils.upperCase(bean.getEmail()));
			invLink.setEmail(StringUtils.upperCase(bean.getEmail()));
		} else if (StringUtils.isBlank(bean.getEmail()) && invLink.getInvitationBy().equals(GlobalVal.INV_BY_SMS)) {
			invLink.setInvitationBy(GlobalVal.INV_BY_SMS);
			invLink.setReceiverDetail(bean.getUserPhone());
		} else if (StringUtils.isNotBlank(bean.getEmail()) && invLink.getInvitationBy().equals(GlobalVal.INV_BY_SMS)) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.cannotuseemailforinvbysms", null, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.INVALID_INV_BY);
		}

		boolean useSyncPrivyVerif = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF);
		invLink.setUseSyncPrivyVerif(useSyncPrivyVerif ? "1" : "0");
		
		daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
	}
	
	private TrInvitationLinkHistory insertInvitationLinkHistory(TrInvitationLink invLink, String ipAddress, boolean isEmailChanged, boolean isNikChanged, boolean isPhoneChanged, MsLov actionType, AuditContext audit) {
		AmMsuser request = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), false, audit);
		
		TrUserDataAccessLog accessLog = new TrUserDataAccessLog();
		accessLog.setDtmCrt(new Date());
		accessLog.setUsrCrt(audit.getCallerId());
		accessLog.setUserDataAccessed(invLink.getAmMsuser());
		accessLog.setUserRequest(request);
		accessLog.setAccessDate(new Date());
		accessLog.setLovActionType(actionType);
		accessLog.setIpAddress(StringUtils.isBlank(ipAddress) ? audit.getCallerId() : ipAddress);
		accessLog.setRequestDetails(buildRequestDetail(isEmailChanged, isNikChanged, isPhoneChanged));
		daoFactory.getUserDataAccessLogDao().insertUserDataAccessLog(accessLog);
		
		TrInvitationLinkHistory history = new TrInvitationLinkHistory();
		history.setDtmCrt(new Date());
		history.setUsrCrt(audit.getCallerId());
		history.setTrUserDataAccessLog(accessLog);
		history.setAddress(invLink.getAddress());
		history.setAmMsuser(invLink.getAmMsuser());
		history.setBusinessLine(invLink.getBusinessLine());
		history.setDailyRegisterAttemptAmount(invLink.getDailyRegisterAttemptAmount());
		history.setDateOfBirth(invLink.getDateOfBirth());
		history.setEmail(invLink.getEmail());
		history.setFullName(invLink.getFullName());
		history.setGender(invLink.getGender());
		history.setIdNo(invLink.getIdNo());
		history.setInvitationBy(invLink.getInvitationBy());
		history.setInvitationCode(invLink.getInvitationCode());
		history.setIsActive(invLink.getIsActive());
		history.setIsEmbed(invLink.getIsEmbed());
		history.setIsRedirectUrl(invLink.getIsRedirectUrl());
		history.setKecamatan(invLink.getKecamatan());
		history.setKelurahan(invLink.getKelurahan());
		history.setKota(invLink.getKota());
		history.setLastRegisterAttempt(invLink.getLastRegisterAttempt());
		history.setLovUserType(invLink.getLovUserType());
		history.setMsBusinessLine(invLink.getMsBusinessLine());
		history.setMsOffice(invLink.getMsOffice());
		history.setMsTenant(invLink.getMsTenant());
		history.setMsVendor(invLink.getMsVendor());
		history.setOffice(invLink.getOffice());
		history.setOtpCode(invLink.getOtpCode());
		history.setPhone(invLink.getPhone());
		history.setPhotoId(invLink.getPhotoId());
		history.setPhotoSelf(invLink.getPhotoSelf());
		history.setPlaceOfBirth(invLink.getPlaceOfBirth());
		history.setProvinsi(invLink.getProvinsi());
		history.setReceiverDetail(invLink.getReceiverDetail());
		history.setRefNumber(invLink.getRefNumber());
		history.setRegion(invLink.getRegion());
		history.setZipCode(invLink.getZipCode());
		daoFactory.getInvitationLinkHistoryDao().insertInvitationLinkHistory(history);
		
		return history;
	}
	
	private String buildRequestDetail(boolean isEmailChanged, boolean isNikChanged, boolean isPhoneChanged) {
		StringBuilder builder = new StringBuilder();
		builder.append("Edit Signer Data: ");
		
		if (isEmailChanged) {
			builder.append("Email ");
		}
		
		if (isNikChanged) {
			builder.append("NIK ");
		}
		
		if (isPhoneChanged) {
			builder.append("Phone");
		}
		
		if (!isEmailChanged && !isNikChanged && !isPhoneChanged) {
			builder.append("No Data Changed.");
		}
		
		return builder.toString();
	}

	private void sendInvitationLinkNotif(SendInvitationLinkNotifBean bean, boolean isUpd, NotificationSendingPoint sendPoint, TrSigningProcessAuditTrail auditTrail, MsVendor vendor, AuditContext audit) throws MessagingException, IOException {
		TrInvitationLink invLink = bean.getInvLink();
		MsTenant tenant = bean.getTenant();
		String emailService = invLink.getInvitationBy().equals(GlobalVal.INV_BY_EMAIL) ? "0" : "1";
		NotificationType type = tenantLogic.getNotificationType(tenant, sendPoint, emailService);
		MsMsgTemplate msg = this.generateMsgInvitationLink(invLink, bean.getLinks(), tenant, type, audit);

		if (type.compareTo(NotificationType.EMAIL) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
			sendEmailInvLink(msg, invLink);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		} else {

			if (type.compareTo(NotificationType.WHATSAPP) == 0) {
				auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
				auditTrail.setNotificationVendor(gateway.getDescription());
				
				sendWaInvLink(invLink, msg, tenant, bean.getOffice(), bean.getBusinessLine(), auditTrail, audit);
			} else if (type.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
				auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
				auditTrail.setNotificationVendor(gateway.getDescription());
				
				sendWaHalosisInvLink(invLink, msg, auditTrail, audit);
			} else {
				auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
				String code = type.compareTo(NotificationType.SMS_JATIS) == 0 ? GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS: GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST;
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, code);
				auditTrail.setNotificationVendor(gateway.getDescription());

				sendSmsInvLink(bean, msg, isUpd, type, auditTrail, audit);
			}
		}
	}
	
	private void sendWaHalosisInvLink(TrInvitationLink invLink, MsMsgTemplate msgTemplate, TrSigningProcessAuditTrail auditTrail, AuditContext audit) throws UnsupportedEncodingException {
		String aesEncryptedLink = commonLogic.encryptMessageToString(invLink.getInvitationCode(), audit);
		String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
		
		MsTenant tenant = invLink.getMsTenant();
		
		HalosisSendWhatsAppRequestBean reqBean = new HalosisSendWhatsAppRequestBean();
		String phoneNumber = StringUtils.isNumeric(invLink.getReceiverDetail()) ? invLink.getReceiverDetail() : invLink.getPhone();
		reqBean.setPhoneNumber(phoneNumber);
		reqBean.setRefNo(invLink.getRefNumber());
		reqBean.setMsTenant(invLink.getMsTenant());
		reqBean.setButtonText(null);
		reqBean.setTemplate(msgTemplate);
		
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(invLink.getMsTenant().getTenantName());
		bodyTexts.add(encrypted);
		if (msgTemplate.getTemplateCode().equals(TEMPLATE_INV_LINK_ACTIVE_DURATION)) {
			bodyTexts.add(String.valueOf(tenant.getInvitationLinkActiveDuration()));
		}
		
		List<String> headerTexts = new ArrayList<>();
		headerTexts.add(msgTemplate.getSubject());
		
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		
		reqBean.setBodyTexts(bodyTexts);
		reqBean.setReservedTrxNo(reservedTrxNo);
		reqBean.setHeaderTexts(headerTexts);
		reqBean.setNotes(phoneNumber + " : Send WhatsApp Account Info");
		reqBean.setIsOtp(false);
		
		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setEmail(auditTrail.getEmail());
		auditTrailBean.setInvLink(invLink);
		auditTrailBean.setLovProcessType(auditTrail.getLovProcessType());
		auditTrailBean.setLovSendingPoint(auditTrail.getLovSendingPoint());
		auditTrailBean.setNotes(auditTrail.getNotes());
		auditTrailBean.setPhone(phoneNumber);
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(auditTrail.getAmMsUser());
		auditTrailBean.setVendorPsre(auditTrail.getMsVendor());
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SEND_WA_GENINV);
		
		if ("1".equals(gs.getGsValue())) {
			balanceValidatorLogic.validateWhatsAppNotifBalanceAvailability(tenant, phoneNumber, audit);
			whatsAppHalosisLogic.sendSynchronousMessage(reqBean, auditTrailBean, audit);
		} else {
			LOG.info("Invitation Link sent via WhatsApp with template : {}.", msgTemplate.getTemplateCode());
		}
	}
	
	private void sendWaInvLink(TrInvitationLink invLink, MsMsgTemplate messageTemplate, MsTenant tenant, MsOffice office, MsBusinessLine businessLine, TrSigningProcessAuditTrail auditTrail, AuditContext audit) throws IOException {
		String aesEncryptedLink = commonLogic.encryptMessageToString(invLink.getInvitationCode(), audit);
		String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
		
		String buttonText = encrypted;
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(invLink.getMsTenant().getTenantName());
		bodyTexts.add(encrypted);
		if (messageTemplate.getTemplateCode().equals(TEMPLATE_INV_LINK_ACTIVE_DURATION)) {
			bodyTexts.add(String.valueOf(tenant.getInvitationLinkActiveDuration()));
		}
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		
		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(messageTemplate);
		request.setBodyTexts(bodyTexts);
		request.setButtonText(buttonText);
		request.setMsTenant(invLink.getMsTenant());
		
		String phoneNumber = StringUtils.isNumeric(invLink.getReceiverDetail()) ? invLink.getReceiverDetail() : invLink.getPhone();
		request.setPhoneNumber(phoneNumber);
		request.setMsOffice(office);
		request.setMsBusinessLine(businessLine);
		request.setRefNo(invLink.getRefNumber());
		request.setNotes(phoneNumber + " : Send WhatsApp Account Info");
		request.setIsOtp(false);
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SEND_WA_GENINV);
		
		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setEmail(auditTrail.getEmail());
		auditTrailBean.setInvLink(invLink);
		auditTrailBean.setLovProcessType(auditTrail.getLovProcessType());
		auditTrailBean.setLovSendingPoint(auditTrail.getLovSendingPoint());
		auditTrailBean.setNotes(auditTrail.getNotes());
		auditTrailBean.setPhone(phoneNumber);
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(auditTrail.getAmMsUser());
		auditTrailBean.setVendorPsre(auditTrail.getMsVendor());
		
		if ("1".equals(gs.getGsValue())) {
			balanceValidatorLogic.validateWhatsAppNotifBalanceAvailability(invLink.getMsTenant(), phoneNumber, audit);
			whatsAppLogic.sendMessageNotAsync(request, auditTrailBean, audit);
		} else {
			LOG.info("Invitation Link sent via WhatsApp with template : {}.", messageTemplate.getTemplateCode());
		}
		
	}
	
	private void sendSmsVfirst(MsMsgTemplate msg, TrInvitationLink invLink, MsTenant tenant, List<String> links,
			boolean isUpd, String custName, MsOffice office, MsBusinessLine businessLine, String refNo, TrSigningProcessAuditTrail auditTrail, AuditContext audit) {
		String phoneNumber = StringUtils.isNumeric(invLink.getReceiverDetail()) ? invLink.getReceiverDetail() : invLink.getPhone();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNumber, msg.getBody(), tenant);
		SendSmsResponse responseSms = smsLogic.sendSms(sendSmsValueFirstRequestBean);

		if (StringUtils.isNotBlank(responseSms.getErrorMsg()) && !isUpd) {
			// Dalam case error send SMS, hapus inv link yang sudah di-generate.
			links.remove(links.size() - 1);
			daoFactory.getInvitationLinkDao().deleteInvitationLink(invLink);
		}

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		if (null != responseSms.getErrorMsg()) {
			insertGenerateInvErrorHistory(tenant, custName, GlobalVal.ERROR_TYPE_ERROR, responseSms.getErrorMsg(),
					vendor, audit);
		}

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,	GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		String notes = invLink.getReceiverDetail() + " : Send SMS Account Info";
		MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
		
		if (responseSms.getErrorCode() == null || 
				(!responseSms.getErrorCode().equals(VFIRST_ERR28682)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR28681)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					-1, String.valueOf(responseSms.getTrxNo()), null, notes, responseSms.getGuid(), office, businessLine, audit);
			LOG.info("Balance Mutation for SMS Inv Link sent with qty -1");
			auditTrail.setResultStatus("1");
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, String.valueOf(responseSms.getTrxNo()), 
					responseSms.getGuid(), phoneNumber, NotificationType.SMS_VFIRST, messageGateway, auditTrail.getLovSendingPoint(), audit);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		} else {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					0, String.valueOf(responseSms.getTrxNo()), null, notes + " error " + responseSms.getErrorCode(),
					responseSms.getGuid(), office, businessLine, audit);
			LOG.info("Balance Mutation for SMS Inv Link sent with qty 0");
			auditTrail.setResultStatus("0");
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		}

	}

	private void sendSmsInvLink(SendInvitationLinkNotifBean bean, MsMsgTemplate msg, boolean isUpd, NotificationType type, TrSigningProcessAuditTrail auditTrail, AuditContext audit) throws IOException {
		TrInvitationLink invLink = bean.getInvLink();
		String logNote = "Send SMS with Invitation Link Success with {}";
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_GENINV);
		String notes = invLink.getReceiverDetail() + " : Send SMS Account Info";
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		if (type.compareTo(NotificationType.SMS_JATIS) == 0) {
			String phoneNumber = StringUtils.isNumeric(invLink.getReceiverDetail()) ? invLink.getReceiverDetail() : invLink.getPhone();
			JatisSmsRequestBean request = new JatisSmsRequestBean(bean.getTenant(), bean.getOffice(), bean.getBusinessLine(), phoneNumber, msg.getBody(), trxNo, bean.getRefNo(), false);
			if ("1".equals(gs.getGsValue())) {
				SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
				auditTrailBean.setEmail(auditTrail.getEmail());
				auditTrailBean.setInvLink(invLink);
				auditTrailBean.setLovProcessType(auditTrail.getLovProcessType());
				auditTrailBean.setLovSendingPoint(auditTrail.getLovSendingPoint());
				auditTrailBean.setNotes(auditTrail.getNotes());
				auditTrailBean.setPhone(phoneNumber);
				auditTrailBean.setTenant(auditTrail.getMsTenant());
				auditTrailBean.setUser(auditTrail.getAmMsUser());
				auditTrailBean.setVendorPsre(auditTrail.getMsVendor());
				
				jatisSmsLogic.sendSmsAndCutBalance(request, null, null, null, notes, audit, auditTrailBean);
			} else {
				LOG.info(logNote, GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);
			}
		} else {
			if ("1".equals(gs.getGsValue())) {
				sendSmsVfirst(msg, invLink, bean.getTenant(), bean.getLinks(), isUpd, bean.getUserName(), bean.getOffice(), bean.getBusinessLine(), bean.getRefNo(), auditTrail, audit);
			} else {
				LOG.info(logNote, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
			}
		}
	}

	private void sendEmailInvLink(MsMsgTemplate msg, TrInvitationLink invLink) throws MessagingException {
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setBodyMessage(msg.getBody());
		emailInfo.setSubject(msg.getSubject());
		String[] to = new String[] { invLink.getReceiverDetail() };
		emailInfo.setTo(to);
		emailInfo.setFrom(fromEmailAddr);
		emailSenderLogic.sendEmail(emailInfo, null);
	}
	
	private void insertGenerateInvErrorHistory(MsTenant tenant, String custName, String errorType, String errorMessage,
			MsVendor vendor, AuditContext audit) {

		MsLov lovModul = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_ERR_HIST_MODULE,
				GlobalVal.CODE_LOV_ERR_HIST_MODULE_GEN_INV);

		TrErrorHistory errorHistory = new TrErrorHistory();
		errorHistory.setMsLov(lovModul);
		errorHistory.setCustName(StringUtils.upperCase(custName));
		errorHistory.setMsTenant(tenant);
		errorHistory.setErrorType(errorType);
		errorHistory.setErrorDate(new Date());
		errorHistory.setErrorMessage(errorMessage);
		errorHistory.setUsrCrt(audit.getCallerId());
		errorHistory.setDtmCrt(new Date());
		errorHistory.setMsVendor(vendor);
		daoFactory.getErrorHistoryDao().insertErrorHistory(errorHistory);

	}
	
	private void checkDuplicateNikInvLink(String nik, List<String> niks, AuditContext audit) {
		if (StringUtils.isBlank(nik)) {
			throw new DocumentException(getMessage("businesslogic.external.idempty", null, audit), ReasonDocument.VAR_EMPTY);
		}

		if (niks.stream().noneMatch(nik::equals)) {
			niks.add(nik);
		} else {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.doublenik",
					new String[] { nik }, retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);
		}
	}

	private String checkDuplicateEmailInvLink(String email, List<String> emails, String emailRegex, AuditContext audit) {
		if (StringUtils.isNotBlank(email)) {
			if (emails.stream().noneMatch(email::equalsIgnoreCase)) {
				emails.add(email);
			} else {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.doubleemail",
						new Object[] { email }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
			}

			if (!email.matches(emailRegex)) {
				if (GlobalVal.CONST_CONFINS.equalsIgnoreCase(audit.getCallerId())) {
					return messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_EMAIL,
							new Object[] { email }, this.retrieveLocaleAudit(audit));
				} else {
					throw new InvitationLinkException(
							messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_EMAIL,
									new Object[] { email }, this.retrieveLocaleAudit(audit)),
							ReasonInvitationLink.INVALID_EMAIL);
				}
			}
		}

		return "";
	}

	private String checkDuplicatePhoneInvLink(String phone, List<String> phones, String phoneNoRegex, String email,
			AuditContext audit) {
		if (StringUtils.isNotBlank(phone)) {
			if (phones.stream().noneMatch(phone::equalsIgnoreCase)) {
				phones.add(phone);
			} else {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.doublephone",
						new Object[] { phone }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_PHONE);
			}

			if (!phone.matches(phoneNoRegex)) {
				if (GlobalVal.CONST_CONFINS.equalsIgnoreCase(audit.getCallerId())) {
					return messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
							new Object[] { phone }, this.retrieveLocaleAudit(audit));
				} else {
					throw new InvitationLinkException(
							messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
									new Object[] { phone }, this.retrieveLocaleAudit(audit)),
							ReasonInvitationLink.INVALID_PHONE_NO);
				}
			}
		}

		return "";
	}
	
	private void checkExistingNikEmailPhone(String phone, String email, String nik, AuditContext audit) {

		AmMsuser nikUser = daoFactory.getUserDao().getUserByIdNo(nik);
		AmMsuser phoneUser = daoFactory.getUserDao().getActiveUserByPhone(phone);
		AmMsuser emailUser = null;
		if (StringUtils.isNotBlank(email)) { 
			emailUser = daoFactory.getUserDao().getUserByLoginId(email);
		}

		if (null != nikUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(nikUser.getIdMsUser(), false);

			if (null != phoneUser && null != emailUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& phoneUser.getLoginId().equals(emailUser.getLoginId())) {

				/*
				 * ESH-1236 NIK, HP, dan Email sudah digunakna untuk pengguna yang sama Throw
				 * error di bawah di-comment supaya bisa melanjutkan proses generate invitation
				 * link dan tidak memberhentikan proses NAP CONFINS
				 */

//				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.userdataused",
//				new String[] {email}, retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);

				return;
			}

			if (null != phoneUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& "1".equals(nikUser.getEmailService()) && StringUtils.isBlank(email)) {

				/*
				 * ESH-1236 Case serupa dengan yang di atas: NIK dan HP sudah digunakan untuk
				 * pengguna yang sama, pengguna tidak memiliki email pribadi Tidak throw error
				 * supaya bisa lanjut proses generate invitation link lainnya dan tidak
				 * memberhentikan proses NAP CONFINS
				 */
				return;
			}

			// Phone user matches NIK user
			if (null != phoneUser && nikUser.getLoginId().equals(phoneUser.getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednikphone",
						new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
						ReasonInvitationLink.DOUBLE_NIK);
			}

			// Email user matches NIK user
			if (null != emailUser && nikUser.getLoginId().equals(emailUser.getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						"businesslogic.invitationlink.emailnikusedinphone",
						new String[] { email, nik, MssTool.maskingString(personalData.getPhoneRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);
			}

			// REJECT
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednik",
					new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
					ReasonInvitationLink.DOUBLE_NIK);
		}

		if (null != emailUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(emailUser.getIdMsUser(), false);

			// Phone user matches Email user
			if (null != phoneUser && emailUser.getLoginId().equals(phoneUser.getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_PHONE_USED_INNIK,
						new String[] { email, phone, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
			}

			// REJECT
			throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_USED_INNIK,
					new String[] { email, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
		}

		if (null != phoneUser) {
			// REJECT
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.phoneusedinemail",
					new String[] { phone, MssTool.maskEmailAddress(phoneUser.getLoginId(), 2) },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_PHONE);
		}
	}
	
	private void checkExistingNikEmailPhoneV2(String phone, String email, String nik, String vendorCode, AuditContext audit) {

		List<String> vendors = new ArrayList<>();
		if (StringUtils.isNotBlank(nik)) {
			vendors = daoFactory.getVendorRegisteredUserDao().getUserRegisteredVendorsByNik(nik);
		}
		
		if (StringUtils.isBlank(phone) && StringUtils.isBlank(email)) {
			throw new InvitationLinkException(getMessage("businesslogic.invitationlink.phoneoremailmustbefilled", null, audit), ReasonInvitationLink.RECEIVER_DETAIL_EMPTY);
		}
		
		AmMsuser nikUser = daoFactory.getUserDao().getUserByIdNo(nik);
		AmMsuser phoneUser = userValidatorLogic.validateGetUserByPhone(phone, false, audit);
		AmMsuser emailUser = null;
		if (StringUtils.isNotBlank(email)) { 
			emailUser = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		}
		
		MsVendorRegisteredUser vruPhone = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		MsVendorRegisteredUser vruEmail = null;
		if (StringUtils.isNotBlank(email)) {
			vruEmail = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendorCode);
		}
		MsVendorRegisteredUser vruNik = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(nik, vendorCode);
		
		TrInvitationLink invLinkByIdNo = StringUtils.isBlank(nik) ? null : daoFactory.getInvitationLinkDao().getInvitationByIdNoAndVendorCode(nik, vendorCode);
		if (null != invLinkByIdNo && "0".equals(invLinkByIdNo.getIsActive()) && null != vruNik && StringUtils.isNotBlank(vruNik.getVendorRegistrationId())) {
			boolean isECertExpired = userValidatorLogic.isCertifExpiredForInquiry(vruNik);
			
			if (!isECertExpired) {
				throw new InvitationLinkException(getMessage("businesslogic.invitationlink.existingidno", new Object[] { nik }, audit), ReasonInvitationLink.UNKOWN);
			}
		}

		List<TrInvitationLink> invLinkByPhone = StringUtils.isBlank(phone) ? null : daoFactory.getInvitationLinkDao().getListInvitationByPhone(phone);
		if (null != invLinkByPhone && !invLinkByPhone.isEmpty()
				&& (null != nik && !nik.isEmpty() && !nik.equals(invLinkByPhone.get(0).getIdNo())) && vruPhone != vruNik) {
			throw new InvitationLinkException(messageSource.getMessage(MSG_EXISTING_PHONE,
					new Object[] { phone }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_PHONE_NO);
		}

		if (null != nikUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(nikUser.getIdMsUser(), false);

			if (null != phoneUser && null != emailUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& phoneUser.getLoginId().equals(emailUser.getLoginId())) {

				/*
				 * ESH-1236 NIK, HP, dan Email sudah digunakna untuk pengguna yang sama Throw
				 * error di bawah di-comment supaya bisa melanjutkan proses generate invitation
				 * link dan tidak memberhentikan proses NAP CONFINS
				 */

//				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.userdataused",
//				new String[] {email}, retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);

				return;
			}

			if (null != phoneUser && nikUser.getLoginId().equals(phoneUser.getLoginId())
					&& (null != vruNik && "1".equals(vruNik.getEmailService())) && StringUtils.isBlank(email)) {

				/*
				 * ESH-1236 Case serupa dengan yang di atas: NIK dan HP sudah digunakan untuk
				 * pengguna yang sama, pengguna tidak memiliki email pribadi Tidak throw error
				 * supaya bisa lanjut proses generate invitation link lainnya dan tidak
				 * memberhentikan proses NAP CONFINS
				 */
				return;
			}
			
			// phone user not match nik user
			if (null != phoneUser && !nikUser.getLoginId().equals(phoneUser.getLoginId())) {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.register.phoneusedbyothernik",
						new String[] { MssTool.maskingString(phone, 8, 0, '*'), MssTool.maskingString(nik, 8, 0, '*') }, retrieveLocaleAudit(audit)),
						ReasonInvitationLink.DOUBLE_NIK);
			}

			// Phone user matches NIK user
			if (null != vruPhone && nikUser.getLoginId().equals(vruPhone.getAmMsuser().getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednikphone",
						new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
						ReasonInvitationLink.DOUBLE_NIK);
			}

			// Email user matches NIK user
			if (null != vruEmail && nikUser.getLoginId().equals(vruEmail.getAmMsuser().getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						"businesslogic.invitationlink.emailnikusedinphone",
						new String[] { email, nik, MssTool.maskingString(personalData.getPhoneRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLE_NIK);
			}

			// REJECT
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
			if (vendors.contains(vendorCode) && vendor.getReregistAvailable().equals("0")) {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.usednik",
						new String[] { MssTool.maskEmailAddress(nikUser.getLoginId(), 2) }, retrieveLocaleAudit(audit)),
						ReasonInvitationLink.DOUBLE_NIK);
			}
		} else if (null != phoneUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(phoneUser.getIdMsUser(), false);
			String masked = generateMaskedInfo(personalData.getIdNoRaw());
			
			//Phone user matches email User
			if (null != emailUser && emailUser.getIdMsUser() == phoneUser.getIdMsUser()) {
				throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_PHONE_USED_INNIK, new Object[] {email, phone, masked}, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
			}
			
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.document.invalidphonenik", new Object[] {masked, nik}, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
		} else if (null != emailUser) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(emailUser.getIdMsUser(), false);
			String masked = generateMaskedInfo(personalData.getIdNoRaw());
			throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_USED_INNIK, new Object[] {email, masked}, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
		}

		// Jika no telp sudah digunakan di invitation lain

		if (null != vruEmail) {
			PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(vruEmail.getAmMsuser().getIdMsUser(), false);

			// Phone user matches Email user
			if (null != vruPhone && vruEmail.getAmMsuser().getLoginId().equals(vruPhone.getAmMsuser().getLoginId())) {
				// REJECT
				throw new InvitationLinkException(messageSource.getMessage(
						GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_PHONE_USED_INNIK,
						new String[] { email, phone, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
						retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
			}

			// REJECT
			throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EMAIL_USED_INNIK,
					new String[] { email, MssTool.maskingString(personalData.getIdNoRaw(), 8, 0, '*') },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_EMAIL);
		}

		if (null != vruPhone) {
			// REJECT
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.phoneusedinemail",
					new String[] { phone, MssTool.maskEmailAddress(vruPhone.getAmMsuser().getLoginId(), 2) },
					retrieveLocaleAudit(audit)), ReasonInvitationLink.DOUBLED_PHONE);
		}
	}
	
	private boolean checkMainVendorRegStatus (String idNo, MsTenant tenant, int totalUser, AuditContext audit) {
		List<String> vendors = new ArrayList<>();
		if (null != idNo) {
			vendors = daoFactory.getVendorRegisteredUserDao().getUserRegisteredVendorsByNik(idNo);
		}
			
		MsVendor mainVendor = vendorValidatorLogic.validateGetMainDefaultVendor(tenant.getTenantCode(), false, audit);
		if (vendors.contains(mainVendor.getVendorCode()) && "1".equals(mainVendor.getIsOperating())) {
			AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(idNo);
			insertUserofTenant(user, tenant, audit);
			if (totalUser > 1) {
				return false;
			} else {
				throw new UserException(messageSource.getMessage("businesslogic.invitationlink.registeredtomainvendor", new Object[] {idNo}, this.retrieveLocaleAudit(audit)),
						ReasonUser.ALREADY_REGISTERED);
			}
				
		}
		
		return true;
	}
	
	private void checkOtherVendorInvitationLink(String phone, String email, String nik, String vendorCode, AuditContext audit) {
		List<TrInvitationLink> otherVendorByIdNo = daoFactory.getInvitationLinkDao().getListInvitationByIdNoOtherVendor(nik, vendorCode);
		if (!otherVendorByIdNo.isEmpty()) {
			for (TrInvitationLink invLink : otherVendorByIdNo) {
				if (!nik.equals(invLink.getIdNo())) {
					throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EXISTING_ID_NO_OTHER_LINK,
							new Object[] { nik }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.DOUBLE_NIK);
				}
			}
		}
		
		List<TrInvitationLink> otherVendorByPhone = daoFactory.getInvitationLinkDao().getListInvitationByPhoneOtherVendor(phone, vendorCode);
		if (!otherVendorByPhone.isEmpty()) {
			for (TrInvitationLink invLink : otherVendorByPhone) {
				if (!nik.equals(invLink.getIdNo())) {
					throw new InvitationLinkException(messageSource.getMessage(MSG_EXISTING_PHONE, new Object[] {phone}, this.retrieveLocaleAudit(audit)), 
							ReasonInvitationLink.INVALID_PHONE_NO);
				}
			}
		}
		
		List<TrInvitationLink> otherVendorEmail = daoFactory.getInvitationLinkDao().getListInvitationByEmailOtherVendor(email, vendorCode);
		if (!otherVendorEmail.isEmpty()) {
			for (TrInvitationLink invLink : otherVendorEmail) {
				if (!nik.equals(invLink.getIdNo())) {
					throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL, new Object[] {email}, this.retrieveLocaleAudit(audit)), 
							ReasonInvitationLink.INVALID_EMAIL);
				}
			}
		}
	}
	
	private String generateInvitationCode() {
		String result = this.createInvCode();
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByInvitationCode(result);
		while (invLink != null) {
			result = this.createInvCode();
			invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByInvitationCode(result);
		}

		return result;
	}
	
	public String createInvCode() {
		
		// random characters
		String totalChars = MssTool.generateRandomCharacters(GlobalVal.CHRS, 2);
			
		// lower case letters
		String lowerCaseLetters = MssTool.generateRandomCharacters(GlobalVal.LOWER_CHRS, 4);
			
		String combinedChars = lowerCaseLetters.concat(totalChars);
		List<Character> pwdChars = combinedChars.chars().mapToObj(c -> (char) c).collect(Collectors.toList());
		Collections.shuffle(pwdChars);
		return pwdChars.stream().collect(StringBuilder::new, StringBuilder::append, StringBuilder::append).toString();
	}
	
	private MsMsgTemplate generateMsgInvitationLink(TrInvitationLink invLink, List<String> links, MsTenant tenant, NotificationType type, AuditContext audit)
			throws UnsupportedEncodingException {
		String aesEncryptedLink = commonLogic.encryptMessageToString(invLink.getInvitationCode(), audit);
		String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
		links.add(regInvLink + encrypted);
		Map<String, Object> link = new HashMap<>();
		link.put("link", regInvLink + encrypted);
		link.put(TENANT_NAME, invLink.getMsTenant().getTenantName());
		link.put(GlobalVal.DURATION, invLink.getMsTenant().getInvitationLinkActiveDuration());
		Map<String, Object> template = new HashMap<>();
		template.put("invite", link);

		MsMsgTemplate msg;
		if (type.compareTo(NotificationType.EMAIL) == 0) {
			if (invLink.getMsTenant().getInvitationLinkActiveDuration() == null || invLink.getMsTenant().getInvitationLinkActiveDuration() == 0) {
				msg = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INV_REG, template);
				return msg;
			} else {
				msg = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INV_REG_EXP, template);
				return msg;
			}
		} 
			
		if (type.compareTo(NotificationType.WHATSAPP) == 0 || type.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
			if (invLink.getMsTenant().getInvitationLinkActiveDuration() == null || invLink.getMsTenant().getInvitationLinkActiveDuration() == 0) {
				msg = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, "inv_link_button2");
				return msg;
			} else {
				msg = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, TEMPLATE_INV_LINK_ACTIVE_DURATION);
				return msg;
			}
		} 
		
		if (type.compareTo(NotificationType.SMS_JATIS) == 0 || type.compareTo(NotificationType.SMS_VFIRST) == 0) {
			msg = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INV_REG_SMS, template);
		} else {
			msg = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INV_REG_SMS_EXP, template);
		}

		return msg;
	}
	
	private String generateMaskedInfo(String toMask) {
		StringBuilder masked = new StringBuilder();
		int length = toMask.length();
		if (StringUtils.isNumeric(toMask)) {
			masked.append(toMask.subSequence(0, 2));
			for (int i = 0; i < length - 4; i++) {
				masked.append("*");
			}
			masked.append(toMask.substring(length - 3));
		} else {
			masked.append(toMask.substring(0, 2));
			for (int i = 0; i < length - 2; i++) {
				masked.append("*");
			}
		}

		return masked.toString();
	}
	
	@Override
	public GeneratInvLinkExternalResponse generateInvLinkExternal(GeneratInvLinkExternalRequest request, String apiKey,
			AuditContext audit) throws Exception {
		
		GeneratInvLinkExternalResponse response = new GeneratInvLinkExternalResponse();
		
		String[] split = apiKey.split("@");
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCodeNewTrx(split[1]);
		MsVendor vendor = StringUtils.isBlank(request.getPsreCode()) ? vendorValidatorLogic.validateGetMainDefaultVendor(tenant.getTenantCode(), false, audit) 
							: vendorValidatorLogic.validateVendorOfTenant(request.getPsreCode(), tenant.getTenantCode(), true, audit);
		Status status = new Status();
		
		try {
			genInvExternalValidator(request, tenant, audit);
			
			Status checkStatus = this.checkPhoneEmailNikExternal(request, vendor, tenant, audit);
			if (0 != checkStatus.getCode()) {
				response.setStatus(checkStatus);
				return response;
			}
			
			if (isNotExistingUserV2(request.getEmail(), request.getIdKtp(), request.getTlp(), tenant, vendor, audit)) {
				NikPhoneEmailValidationBean valBean = new NikPhoneEmailValidationBean();
				valBean.setEmail(request.getEmail());
				valBean.setNik(request.getIdKtp());
				valBean.setPhone(request.getTlp());
				valBean.setIpAddress(audit.getCallerId());
				
				TrInvitationLink invLink =  this.checkExistingNikEmailPhoneInvitationLinkV2(valBean, vendor.getVendorCode(), true, tenant.getTenantCode(), audit);
				
				List<String> types = daoFactory.getLovDao().getListofCodeByLovGroup(GlobalVal.LOV_GROUP_USER_TYPE);
				if (!types.contains(request.getType())) {
					throw new UserException(messageSource.getMessage("businesslogic.user.invalidusertype", new Object[] {request.getType()}, this.retrieveLocaleAudit(audit))
							, ReasonUser.PARAM_INVALID);
				}
				
				if (StringUtils.isNotBlank(request.getJenisKelamin()) && !GlobalVal.CODE_LOV_MALE.equalsIgnoreCase(request.getJenisKelamin())
						&& !GlobalVal.CODE_LOV_FEMALE.equalsIgnoreCase(request.getJenisKelamin())) {
					throw new UserException(this.messageSource.getMessage("businesslogic.user.invalidgender", null,
							this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_GENDER_CODE);
				}

				if (invLink == null) {
					if (StringUtils.isNotBlank(request.getEmail())) {
						invLink = this.insertInvitationLinkExternal(request, GlobalVal.INV_BY_EMAIL, request.getEmail(),
								tenant, vendor, audit);
					} else {
						invLink = this.insertInvitationLinkExternal(request, GlobalVal.INV_BY_SMS, request.getTlp(),
								tenant, vendor, audit);
					}
				} else {
					this.updateInvitaitonLinkExternal(request, invLink, tenant, audit);
				}
				
				String aesEncryptedLink = commonLogic.encryptMessageToString(invLink.getInvitationCode(), audit);
				String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
				response.setLink(regInvLink + encrypted);
				
				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
				MsLov process = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_INV_LINK_REQ);
				auditTrail.setLovProcessType(process);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendor);
				auditTrail.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
				auditTrail.setDtmCrt(new Date());
				auditTrail.setEmail(StringUtils.isNotBlank(request.getEmail()) ? request.getEmail() : null);
				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getTlp()));
				auditTrail.setHashedPhoneNo( MssTool.getHashedString(request.getTlp()));
				auditTrail.setNotes(invLink.getInvitationCode());
				auditTrail.setTrInvitationLink(invLink);
				auditTrail.setResultStatus("1");
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
			} else {
				response.setLink("");
			}
			
		} catch (NullPointerException e) {
			String custName = "";
			if (StringUtils.isNotBlank(request.getNama())) {
				custName = request.getNama();
			} else if (StringUtils.isNotBlank(request.getEmail())) {
				custName = request.getEmail();
			} else {
				custName = request.getTlp();
			}
			
			// Message NPE biasanya kosong, ada message sendiri jika message NPE kosong
			String npeMessage = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage()
					: "NullPointerException caught";

			insertGenerateInvErrorHistory(tenant, custName, GlobalVal.ERROR_TYPE_ERROR, npeMessage, vendor, audit);
			status.setCode(StatusCode.UNKNOWN);
			status.setMessage(npeMessage);
			response.setStatus(status);
			
			throw e;

		} catch (UserException | ParseException | NoSuchMessageException | UnsupportedEncodingException e) {
			String custName = "";
			if (StringUtils.isNotBlank(request.getNama())) {
				custName = request.getNama();
			} else if (StringUtils.isNotBlank(request.getEmail())) {
				custName = request.getEmail();
			} else {
				custName = request.getTlp();
			}
			
			insertGenerateInvErrorHistory(tenant, custName, GlobalVal.ERROR_TYPE_ERROR, e.getLocalizedMessage(), vendor,
					audit);
			status.setCode(StatusCode.UNKNOWN);
			status.setMessage(e.getLocalizedMessage());
			response.setStatus(status);
			
			String msg = e.getLocalizedMessage().contains("Unparseable") ? messageSource.getMessage("businesslogic.global.dateformat", new Object[] {"Tanggal Lahir", GlobalVal.DATE_FORMAT}, 
					this.retrieveLocaleAudit(audit)) : e.getLocalizedMessage();
			
			if (e.getLocalizedMessage().contains("Unparseable")) {
				throw new UserException(msg, ReasonUser.INVALID_FORMAT);
			} else {
				throw e;
			}
			
		}
		
		status.setCode(0);
		if (StringUtils.isBlank(response.getLink())) {
			status.setMessage(messageSource.getMessage("businesslogic.user.alreadyregistered", null, this.retrieveLocaleAudit(audit)));
		}
		
		response.setStatus(status);
		
		return response;
	}
	
	private void genInvExternalValidator(GeneratInvLinkExternalRequest request, MsTenant tenant, AuditContext audit) {
		if (StringUtils.isBlank(request.getNama())) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {"Nama"}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		}
		
		if (StringUtils.isBlank(request.getTglLahir())) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {"Tgl. Lahir"}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		}
		
		if (StringUtils.isBlank(request.getIdKtp())) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {"Id KTP/ NIK"}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		}
		
		if (StringUtils.isBlank(request.getTlp())) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {"Tlp"}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		}
		
		if ("0".equals(tenant.getEmailService()) && StringUtils.isBlank(request.getEmail())) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {"Email"}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		}
		
		if (StringUtils.isBlank(request.getType())) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {"type"}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		}
		
	}
	
	private Status checkPhoneEmailNikExternal(GeneratInvLinkExternalRequest request, MsVendor vendor, MsTenant tenant, 
			AuditContext audit) {
		Status status = new Status();
		Map<Integer, String> errors = new HashMap<>();

		AmGeneralsetting gsPhone = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue()
				: regexPhone;
		AmGeneralsetting gsEmail = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT);
		String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue()
				: regexEmail;

		try {
			if ("0".equals(tenant.getEmailService()) && StringUtils.isBlank(request.getEmail())) {
				throw new UserException(messageSource.getMessage("businesslogic.document.emptyemail", null,
						this.retrieveLocaleAudit(audit)), ReasonUser.EMAIL_EMPTY);
			}
			
			if (StringUtils.isNotBlank(request.getEmail()) && !request.getEmail().matches(emailRegex)) {
				errors.put(0, messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_EMAIL,
						new Object[] { request.getEmail() }, this.retrieveLocaleAudit(audit)));
			}
			
			if (!request.getTlp().matches(phoneNoRegex)) {
				errors.put(0, messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
							new Object[] { request.getTlp() }, this.retrieveLocaleAudit(audit)));
			}
			
			if (StringUtils.isNotBlank(request.getIdKtp())) {
				if (request.getIdKtp().length() != 16) {
					throw new UserException(this.messageSource.getMessage("businesslogic.user.invalidniklength", null,
							this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_NIK_LENGTH);
				}
				
				if (!StringUtils.isNumeric(request.getIdKtp())) {
					throw new ParameterException(getMessage("businesslogic.user.nikisnotnumber", null, audit), ReasonParam.NIK_MUST_BE_NUMERIC);
				}
			}
			
			this.checkExistingNikEmailPhoneV2(request.getTlp(), request.getEmail(), request.getIdKtp(),
					vendor.getVendorCode(), audit);
			

		} catch (UserException | InvitationLinkException e) {
				
			String userName = null;
			if (StringUtils.isNotBlank(request.getNama())) {
				userName = request.getNama();
			} else {
				userName = StringUtils.isBlank(request.getNama()) ? request.getTlp() :request.getEmail();
			}
				
			audit.setCallerId(userName);
			insertGenerateInvErrorHistory(tenant, userName, GlobalVal.ERROR_TYPE_REJECT, e.getLocalizedMessage(),
						vendor, audit);
			status.setCode(e.getErrorCode());
			status.setMessage(e.getLocalizedMessage());
			return status;
		}

		if (!errors.isEmpty()) {
			status.setCode(9999);
			StringBuilder msg = new StringBuilder();
			for (int i = 0; i < errors.size(); i++) {
				if (StringUtils.isNotBlank(msg.toString())) {
					msg.append(" | " + errors.get(i));
				} else {
					msg.append(errors.get(i));
				}
			}
			status.setMessage(msg.toString());
			return status;
		}

		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		return status;
	}
	
	private void updateInvitaitonLinkExternal(GeneratInvLinkExternalRequest bean, TrInvitationLink invLink, MsTenant tenant, AuditContext audit)
			throws ParseException {
		boolean isEmailChanged = !Objects.equals(invLink.getEmail(), bean.getEmail());
		boolean isPhoneChanged = !Objects.equals(invLink.getPhone(), bean.getTlp());
		boolean isNikChanged = !Objects.equals(invLink.getIdNo(), bean.getIdKtp());				
		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_UPDATE_INV_LINK);
		if (isEmailChanged || isPhoneChanged || isNikChanged) {
			insertInvitationLinkHistory(invLink, null, isEmailChanged, isNikChanged, isPhoneChanged,actionType, audit);
		}
		
		invLink.setMsTenant(tenant);
		invLink.setGender(StringUtils.upperCase(bean.getJenisKelamin()));
		invLink.setKelurahan(StringUtils.upperCase(bean.getKelurahan()));
		invLink.setKecamatan(StringUtils.upperCase(bean.getKecamatan()));
		invLink.setKota(StringUtils.upperCase(bean.getKota()));
		invLink.setZipCode(StringUtils.upperCase(bean.getKodePos()));
		invLink.setPlaceOfBirth(StringUtils.upperCase(bean.getTmpLahir()));
		invLink.setFullName(bean.getNama());

		if (StringUtils.isNotBlank(bean.getTglLahir())) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			sdf.setLenient(false);
			invLink.setDateOfBirth(sdf.parse(bean.getTglLahir()));
		}

		invLink.setProvinsi(StringUtils.upperCase(bean.getProvinsi()));

		if (StringUtils.isNotBlank(bean.getSelfPhoto())) {
			byte[] selfPhotoByteArray = MssTool.imageStringToByteArray(bean.getSelfPhoto());
			String key = cloudStorageLogic.storeRegistrationSelfie(bean.getIdKtp(), selfPhotoByteArray);

			invLink.setPhotoSelf(key.getBytes());
		} else {
			invLink.setPhotoSelf(null);
		}

		if (StringUtils.isNotBlank(bean.getIdPhoto())) {
			byte[] idPhotoByteArray = MssTool.imageStringToByteArray(bean.getIdPhoto());
			String key = cloudStorageLogic.storeRegistrationKtp(bean.getIdKtp(), idPhotoByteArray);

			invLink.setPhotoId(key.getBytes());
		} else {
			invLink.setPhotoId(null);
		}

		invLink.setIdNo(bean.getIdKtp());
		invLink.setAddress(StringUtils.upperCase(bean.getAlamat()));
		invLink.setPhone(bean.getTlp());
		invLink.setEmail(StringUtils.upperCase(bean.getEmail()));
		invLink.setRegion(StringUtils.upperCase(bean.getRegion()));
		invLink.setOffice(StringUtils.upperCase(bean.getOffice()));
		invLink.setBusinessLine(StringUtils.upperCase(bean.getBusinessLine()));
		invLink.setRefNumber(StringUtils.upperCase(bean.getTaskNo()));
		invLink.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));
		invLink.setDtmUpd(new Date());

		if (StringUtils.isNotBlank(bean.getEmail()) && invLink.getInvitationBy().equals(GlobalVal.INV_BY_EMAIL)) {
			invLink.setInvitationBy(GlobalVal.INV_BY_EMAIL);
			invLink.setReceiverDetail(StringUtils.upperCase(bean.getEmail()));
		} else if (StringUtils.isBlank(bean.getEmail()) && invLink.getInvitationBy().equals(GlobalVal.INV_BY_SMS)) {
			invLink.setInvitationBy(GlobalVal.INV_BY_SMS);
			invLink.setReceiverDetail(bean.getTlp());
		}

		boolean useSyncPrivyVerif = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF);
		invLink.setUseSyncPrivyVerif(useSyncPrivyVerif ? "1" : "0");

		daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
	}
	
	private TrInvitationLink insertInvitationLinkExternal(GeneratInvLinkExternalRequest bean, String invBy, String receiverDetail,
			MsTenant tenant, MsVendor vendor, AuditContext audit) throws ParseException {
		TrInvitationLink invLink = new TrInvitationLink();
		Date now = new Date();
		invLink.setInvitationBy(invBy);
		invLink.setReceiverDetail(StringUtils.upperCase(receiverDetail));
		invLink.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		invLink.setDtmCrt(now);
		invLink.setIsActive("1");
		invLink.setMsTenant(tenant);
		invLink.setGender(StringUtils.upperCase(bean.getJenisKelamin()));
		invLink.setKelurahan(StringUtils.upperCase(bean.getKelurahan()));
		invLink.setKecamatan(StringUtils.upperCase(bean.getKecamatan()));
		invLink.setKota(StringUtils.upperCase(bean.getKota()));
		invLink.setZipCode(StringUtils.upperCase(bean.getKodePos()));
		invLink.setPlaceOfBirth(StringUtils.upperCase(bean.getTmpLahir()));
		invLink.setFullName(bean.getNama());
		invLink.setRegion(StringUtils.upperCase(bean.getRegion()));
		invLink.setOffice(StringUtils.upperCase(bean.getOffice()));
		invLink.setRefNumber(StringUtils.upperCase(bean.getTaskNo()));
		invLink.setBusinessLine(StringUtils.upperCase(bean.getBusinessLine()));
		invLink.setUsrUpd(audit.getCallerId());
		invLink.setDtmUpd(now);

		if (null == vendor) {
			List<MsVendoroftenant> vots = daoFactory.getVendorDao()
					.getListVendoroftenantByTenantCode(tenant.getTenantCode());
			if (vots.size() == 1) {
				invLink.setMsVendor(vots.get(0).getMsVendor());
			} else {
				invLink.setMsVendor(getUnregisteredVendor(vots, invLink.getReceiverDetail()));
			}
		} else {
			invLink.setMsVendor(vendor);
		}

		if (StringUtils.isNotBlank(bean.getTglLahir())) {
			// Set lenient false supaya throw error jika format tidak sesuai
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.setLenient(false);
			invLink.setDateOfBirth(sdf.parse(bean.getTglLahir()));
		}

		invLink.setProvinsi(StringUtils.upperCase(bean.getProvinsi()));

		if (StringUtils.isNotBlank(bean.getSelfPhoto())) {
			byte[] selfPhotoByteArray = MssTool.imageStringToByteArray(bean.getSelfPhoto());
			String key = cloudStorageLogic.storeRegistrationSelfie(bean.getIdKtp(), selfPhotoByteArray);

			invLink.setPhotoSelf(key.getBytes());
		} else {
			invLink.setPhotoSelf(null);
		}

		if (StringUtils.isNotBlank(bean.getIdPhoto())) {
			byte[] idPhotoByteArray = MssTool.imageStringToByteArray(bean.getIdPhoto());
			String key = cloudStorageLogic.storeRegistrationKtp(bean.getIdKtp(), idPhotoByteArray);

			invLink.setPhotoId(key.getBytes());
		} else {
			invLink.setPhotoId(null);
		}
		
		MsLov userType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_USER_TYPE, bean.getType());
		invLink.setLovUserType(userType); 
		
		invLink.setIdNo(bean.getIdKtp());
		invLink.setAddress(StringUtils.upperCase(bean.getAlamat()));
		invLink.setPhone(bean.getTlp());
		invLink.setEmail(bean.getEmail());
		if (invLink.getInvitationBy().equalsIgnoreCase(GlobalVal.INV_BY_EMAIL)) {
			invLink.setEmail(StringUtils.upperCase(receiverDetail));
		} else {
			invLink.setPhone(receiverDetail);
		}

		invLink.setInvitationCode(generateInvitationCode());

		boolean useSyncPrivyVerif = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF);
		invLink.setUseSyncPrivyVerif(useSyncPrivyVerif ? "1" : "0");

		daoFactory.getInvitationLinkDao().insertInvitationLink(invLink);
		LOG.info("Invitation Link with code {} and receiver detail {} inserted", invLink.getInvitationCode(), invLink.getReceiverDetail());

		return invLink;
	}
	
	private MsVendor getUnregisteredVendor(List<MsVendoroftenant> vots, String receiverDetail) {
		for (MsVendoroftenant vot : vots) {
			TrInvitationLink exist = daoFactory.getInvitationLinkDao()
					.getInvitationLinkByRecieverDetailAndIdMsVendor(receiverDetail,
							vot.getMsVendor().getIdMsVendor());

			if (null == exist) {
				return vot.getMsVendor();
			}
		}
		
		return null;
	}
	
	@Override
	public RegenerateInvitationResponse regenerateInvitation(RegenerateInvitationRequest request, AuditContext audit) throws MessagingException, IOException {
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByRecieverDetailV2(request.getReceiverDetail(), request.getVendorCode());
		commonValidatorLogic.validateNotNull(invLink, getMessage("businesslogic.invitationlink.invitationlinknotexistwithdata", null, audit), StatusCode.INV_LINK_NOT_EXIST);
		
		if(invLink.getInvitationBy().equals(GlobalVal.INV_BY_EMAIL)) {
			invLink.setEmail(invLink.getReceiverDetail());
		} else {
			invLink.setPhone(invLink.getReceiverDetail());
		}
		
		MsTenant tenant = invLink.getMsTenant();
		MsVendor vendor = invLink.getMsVendor();
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		if(!"1".equals(vot.getAllowRegenerateInvLink())) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.cannotregenerateforvendor", new Object[] {vendor.getVendorCode()}, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.CANNOT_REGENERATE);
		}
		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_REGEN_INV_LINK);
		insertInvitationLinkHistory(invLink, request.getIpAddress(), false, false, false,actionType, audit);
		try {		
			MsVendorRegisteredUser vru = getVru(invLink.getIdNo(), invLink.getEmail(), invLink.getPhone(), vendor.getVendorCode());
			if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_VIDA)) {
				boolean isECertExpired = null != vru && StringUtils.isNotBlank(vru.getVendorRegistrationId()) && userValidatorLogic.isCertifExpiredForRegister(vru, audit);
				boolean isHasVendorRegistrationId = null != vru && StringUtils.isNotBlank(vru.getHashedSignerRegisteredPhone());

				if (isHasVendorRegistrationId && !isECertExpired) {
					userValidatorLogic.validateNikPhoneEmailForRegisterDefaultVendor(invLink.getIdNo(), invLink.getPhone(), invLink.getEmail(), invLink.getMsVendor(), audit);
				}
			} else {
				if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID) && null != vru && null != vru.getVendorRegistrationId()) {
						throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.cannotregenerate", null, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.CANNOT_REGENERATE);
				}
			}
		} catch (RegisterException e) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.cannotregenerate", null, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.CANNOT_REGENERATE);
		}
		
		invLink.setInvitationCode(generateInvitationCode());
		invLink.setUsrUpd(audit.getCallerId());
		invLink.setIsActive("1");
		invLink.setDtmUpd(new Date());
		daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		
		List<String> links = new ArrayList<>();
		
		MsOffice office = invLink.getMsOffice();
		MsBusinessLine businessLine = invLink.getMsBusinessLine();
		SendInvitationLinkNotifBean sendNotifBean = new SendInvitationLinkNotifBean();
		sendNotifBean.setInvLink(invLink);
		sendNotifBean.setLinks(links);
		sendNotifBean.setTenant(tenant);
		sendNotifBean.setUserName(invLink.getFullName());
		sendNotifBean.setOffice(office);
		sendNotifBean.setBusinessLine(businessLine);
		sendNotifBean.setRefNo(invLink.getRefNumber());
		
		TrSigningProcessAuditTrail auditTrail = insertAuditTrailRegenInv(invLink, NotificationSendingPoint.REGEN_INV, request.getIpAddress(), GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REGEN_INV, audit);
		sendInvitationLinkNotif(sendNotifBean, false, NotificationSendingPoint.REGEN_INV, auditTrail, vendor, audit);
		links = sendNotifBean.getLinks();
		
		RegenerateInvitationResponse response = new RegenerateInvitationResponse();
		response.setLink(links.get(0));
		Status status = new Status();
		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}
	
	private MsVendorRegisteredUser getVru(String idNo, String email, String phone, String vendorCode) {
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(idNo, vendorCode);
		if (null == vru) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		}
		
		if (null == vru) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendorCode);
		}
		
		return vru;
	}
	
	private TrSigningProcessAuditTrail insertAuditTrailRegenInv(TrInvitationLink invLink, NotificationSendingPoint sendPoint, String ipAddress, String processType, AuditContext audit) {
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, sendPoint.getLovCode());
		MsLov process = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, processType);
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setUsrCrt(audit.getCallerId());
		auditTrail.setDtmCrt(new Date());
		auditTrail.setEmail(invLink.getEmail());
		if (StringUtils.isNotBlank(invLink.getPhone())) {
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(invLink.getPhone()));
			auditTrail.setHashedPhoneNo( MssTool.getHashedString(invLink.getPhone()));
		}
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setLovProcessType(process);
		auditTrail.setMsTenant(invLink.getMsTenant());
		auditTrail.setMsVendor(invLink.getMsVendor());
		auditTrail.setNotes(invLink.getInvitationCode());
		auditTrail.setOtpCode(invLink.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setTrInvitationLink(invLink);
		
		return auditTrail;
	}
	
	@Override
	public UpdateInvDataResponse updateDataInvRegis(UpdateInvDataRequest request, AuditContext audit)
			throws IOException {
		UpdateInvDataResponse response = new UpdateInvDataResponse();
		Status status = new Status();

		commonValidatorLogic.validateNotNull(request.getVendorCode(), getMessage("businesslogic.user.vendorcannotbeempty", null, audit), StatusCode.EMPTY_VENDOR);

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		commonValidatorLogic.validateNotNull(vendor, getMessage(MSG_VENDORCODE_INVALID, null, audit), StatusCode.INV_LINK_INVALID_EMAIL);

		TrInvitationLink invLink = daoFactory.getInvitationLinkDao()
				.getInvitationLinkByRecieverDetailAndIdMsVendor(request.getOldRecieverDetail(), vendor.getIdMsVendor());
		commonValidatorLogic.validateNotNull(invLink, getMessage("businesslogic.invitationlink.invitationlinknotexistwithdata", null, audit), StatusCode.INACTIVE_LINK);
		if ("0".equals(invLink.getIsActive())) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.inactivelink",
					null, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INACTIVE_LINK);
		}
		if(StringUtils.isBlank(request.getPhone())) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.phonenull", null, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.PHONE_NO_NULL);
		}
		
		deletingOldInvLInk(request, invLink, audit);
		
		// user belum aktivasi karena invLink masih aktif
		if ("1".equals(this.checkInvRegisStatus(invLink, "reg", audit))) {
			if ("1".equals(vendor.getEditAfterRegister())) {
				if (GlobalVal.INV_BY_EMAIL.equals(invLink.getInvitationBy())) {
					this.updateRegisteredInvEmail(request, invLink, vendor, audit);
				} else {
					this.updateRegisteredInvSms(request, invLink, vendor, audit);
				}
			} else {
				throw new InvitationLinkException(
						this.messageSource.getMessage("businesslogic.invitationlink.cannotEditUser",
								new Object[] { vendor.getVendorName() }, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.CANNOT_UPDATE);
			}
		} else {
			this.updateUnregInvLink(invLink, request, vendor, audit);
		}

		invLink.setDtmUpd(new Date());
		invLink.setUsrUpd(audit.getCallerId());
		daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);

		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}
	
	private void deletingOldInvLInk(UpdateInvDataRequest request, TrInvitationLink invLink, AuditContext audit) {
		boolean isChangeNIK = StringUtils.isNotBlank(request.getIdNo()) && !request.getIdNo().equals(invLink.getIdNo());
		boolean isChangePhone = StringUtils.isNotBlank(request.getPhone()) && !request.getPhone().equals(invLink.getPhone());
		boolean isChangeEmail = !Objects.equals(request.getEmail(), invLink.getEmail());
		
		MsLov actionType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_LOG_ACTION_TYPE, GlobalVal.CODE_LOV_LOG_ACTION_TYPE_UPDATE_INV_LINK);
		TrInvitationLink invLinkOldPhone = 	daoFactory.getInvitationLinkDao().getInvitationLinkByPhoneAndVendorCodeOtherId(invLink.getPhone(), invLink.getMsVendor().getVendorCode(), invLink.getIdInvitationLink());
		if (invLinkOldPhone != null) {
			TrInvitationLinkHistory history = insertInvitationLinkHistory(invLinkOldPhone, request.getIpAddress(), isChangeEmail, isChangeNIK, isChangePhone,actionType, audit);
			addInvitatioLinkHistoryToExistingAuditTrail(history, invLinkOldPhone, audit);
			daoFactory.getInvitationLinkDao().deleteInvitationLink(invLinkOldPhone);
		}
			
		TrInvitationLink invLinkOldEmail = daoFactory.getInvitationLinkDao().getInvitationLinkByEmailAndVendorCodeOtherId(invLink.getEmail(), invLink.getMsVendor().getVendorCode(), invLink.getIdInvitationLink());
		if(invLinkOldEmail != null && StringUtils.isNotBlank(invLink.getEmail())  ) {
			TrInvitationLinkHistory history = insertInvitationLinkHistory(invLinkOldEmail, request.getIpAddress(), isChangeEmail, isChangeNIK, isChangePhone,actionType, audit);
			addInvitatioLinkHistoryToExistingAuditTrail(history, invLinkOldEmail, audit);
			daoFactory.getInvitationLinkDao().deleteInvitationLink(invLinkOldEmail);
		}
		
		if (StringUtils.isNotBlank(request.getPhone())) {
			TrInvitationLink invLinkExistingPhone = daoFactory.getInvitationLinkDao().getInvitationLinkByPhoneAndVendorCodeOtherId(request.getPhone(), invLink.getMsVendor().getVendorCode(), invLink.getIdInvitationLink());
			if (invLinkExistingPhone != null ) {
				TrInvitationLinkHistory history = insertInvitationLinkHistory(invLinkExistingPhone, request.getIpAddress(), isChangeEmail, isChangeNIK, isChangePhone,actionType, audit);
				addInvitatioLinkHistoryToExistingAuditTrail(history, invLinkExistingPhone, audit);
				daoFactory.getInvitationLinkDao().deleteInvitationLink(invLinkExistingPhone);
			}
		}
		
		if (StringUtils.isNotBlank(request.getEmail())) {
			TrInvitationLink invLinkExistingEmail = daoFactory.getInvitationLinkDao().getInvitationLinkByEmailAndVendorCodeOtherId(request.getEmail(), invLink.getMsVendor().getVendorCode(), invLink.getIdInvitationLink());
			if(invLinkExistingEmail != null ) {
				TrInvitationLinkHistory history = insertInvitationLinkHistory(invLinkExistingEmail, request.getIpAddress(), isChangeEmail, isChangeNIK, isChangePhone,actionType, audit);
				addInvitatioLinkHistoryToExistingAuditTrail(history, invLinkExistingEmail, audit);
				daoFactory.getInvitationLinkDao().deleteInvitationLink(invLinkExistingEmail);
			}
		}
		
		this.insertInvitationLinkHistory(invLink, request.getIpAddress(), isChangeEmail, isChangeNIK, isChangePhone, actionType, audit);
	}

	private void updateUnregInvLink(TrInvitationLink invLink, UpdateInvDataRequest request, MsVendor vendor, AuditContext audit) {
		this.checkReceiverDetail(request, audit);

		String email = StringUtils.isNotBlank(request.getEmail()) && !StringUtils.upperCase(request.getEmail()).equals(invLink.getEmail())
				? request.getEmail() 
				: "";
		String phone = StringUtils.isNotBlank(request.getPhone()) && !request.getPhone().equals(invLink.getPhone())
				? request.getPhone()
				: "";
		String idno = StringUtils.isNotBlank(request.getIdNo()) && !request.getIdNo().equals(invLink.getIdNo())
				? request.getIdNo()
				: "";

		this.checkPhoneEmailNik(phone, email, idno, null, request.getIdNo(), vendor.getVendorCode(), audit);

		// update invitationBy
		invLink.setInvitationBy(request.getInvitationBy());
		invLink.setReceiverDetail(StringUtils.upperCase(request.getReceiverDetail()));
		if (request.getInvitationBy().equals(GlobalVal.INV_BY_SMS)) {
			invLink.setEmail(null);
		} else if (StringUtils.isNotBlank(request.getEmail())
				&& (!request.getInvitationBy().equals(GlobalVal.INV_BY_SMS))) {
			invLink.setEmail(StringUtils.upperCase(request.getEmail()));
		}
		if (StringUtils.isNotBlank(request.getPhone())) {
			invLink.setPhone(StringUtils.upperCase(request.getPhone()));
		}
		if (StringUtils.isNotBlank(request.getIdNo())) {
			invLink.setIdNo(StringUtils.upperCase(request.getIdNo()));
		}
		if (StringUtils.isNotBlank(request.getFullName())) {
			invLink.setFullName(StringUtils.upperCase(request.getFullName()));
		}
		if (StringUtils.isNotBlank(request.getGender())) {
			invLink.setGender(StringUtils.upperCase(request.getGender()));
		}
		if (StringUtils.isNotBlank(request.getPlaceOfBirth())) {
			invLink.setPlaceOfBirth(StringUtils.upperCase(request.getPlaceOfBirth()));
		}
		if (StringUtils.isNotBlank(request.getDateOfBirth())) {
			invLink.setDateOfBirth(MssTool.formatStringToDate(request.getDateOfBirth(), GlobalVal.DATE_FORMAT));
		}
		if (StringUtils.isNotBlank(request.getAddress())) {
			invLink.setAddress(StringUtils.upperCase(request.getAddress()));
		}
		if (StringUtils.isNotBlank(request.getKelurahan())) {
			invLink.setKelurahan(StringUtils.upperCase(request.getKelurahan()));
		}
		if (StringUtils.isNotBlank(request.getKecamatan())) {
			invLink.setKecamatan(StringUtils.upperCase(request.getKecamatan()));
		}
		if (StringUtils.isNotBlank(request.getKota())) {
			invLink.setKota(StringUtils.upperCase(request.getKota()));
		}
		if (StringUtils.isNotBlank(request.getProvinsi())) {
			invLink.setProvinsi(StringUtils.upperCase(request.getProvinsi()));
		}
		if (StringUtils.isNotBlank(request.getZipCode())) {
			invLink.setZipCode(StringUtils.upperCase(request.getZipCode()));
		}
	}

	private void checkReceiverDetail(UpdateInvDataRequest request, AuditContext audit) {
		if (GlobalVal.INV_BY_EMAIL.equals(request.getInvitationBy())) {
			if (StringUtils.isNumeric(request.getReceiverDetail())
					|| StringUtils.isBlank(request.getReceiverDetail())) {
				throw new InvitationLinkException(messageSource.getMessage(MSG_INVALID_RECEIVER_DETAIL,
						new Object[] { GlobalVal.INV_BY_EMAIL }, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.INVALID_RECEIVER_DETAIL);
			}

			if (!request.getReceiverDetail().equals(request.getEmail())) {
				throw new InvitationLinkException(messageSource.getMessage(MSG_MISMATCH_RECEIVER_DETAIL,
						new Object[] { GlobalVal.INV_BY_EMAIL }, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.MISMATCH_RECEIVER_DETAIL);
			}
		} else {
			if (!StringUtils.isNumeric(request.getReceiverDetail())
					|| StringUtils.isBlank(request.getReceiverDetail())) {
				throw new InvitationLinkException(messageSource.getMessage(MSG_INVALID_RECEIVER_DETAIL,
						new Object[] { PHONE }, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.INVALID_RECEIVER_DETAIL);
			}

			if (!request.getReceiverDetail().equals(request.getPhone())) {
				throw new InvitationLinkException(messageSource.getMessage(MSG_MISMATCH_RECEIVER_DETAIL,
						new Object[] { PHONE }, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.MISMATCH_RECEIVER_DETAIL);
			}
		}
	}
	
	private void checkPhoneEmailNik(String phone, String email, String nik, String registeredNik, String toBeInsertedNik, String vendorCode,
			AuditContext audit) {
		AmGeneralsetting gsPhone = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue()
				: regexPhone;
		AmGeneralsetting gsEmail = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT);
		String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue()
				: regexEmail;

		checkNik(nik, vendorCode, audit);
		checkPhone(phone, phoneNoRegex, toBeInsertedNik, registeredNik, audit);
		checkEmail(email, emailRegex, toBeInsertedNik, registeredNik, audit);
	}
	
	private void checkNik(String nik, String vendorCode, AuditContext audit) {
		AmMsuser userByIdNo = daoFactory.getUserDao().getUserByIdNo(nik);
		if (null != userByIdNo) {
			MsVendorRegisteredUser vruById = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByIdMsUserAndVendorCode(userByIdNo.getIdMsUser(), vendorCode);
			if (null != vruById) {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.nikalreadyregistered1",
						new Object[] { nik }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.UNKOWN);
			}
		}
		TrInvitationLink invLinkByIdNo = daoFactory.getInvitationLinkDao().getInvitationByIdNoAndVendorCode(nik, vendorCode);
		if (null != invLinkByIdNo) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.existingidno",
					new Object[] { nik }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.UNKOWN);
		}
	}

	private void checkPhone(String phone, String phoneNoRegex, String nik, String registeredNik, AuditContext audit) {
		if (StringUtils.isNotBlank(phone) && !phone.matches(phoneNoRegex)) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.invalidphoneno",
					new Object[] { phone }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_PHONE_NO);
		}

		AmMsuser userByPhone = StringUtils.isBlank(phone) ? null
				: userValidatorLogic.validateGetUserByPhone(phone, false, audit);
		if (null != userByPhone) {
			PersonalDataBean bean = daoFactory.getUserDao().getUserDataByIdMsUser(userByPhone.getIdMsUser(), false);
			if ((null != nik && !nik.isEmpty() && !nik.equals(bean.getIdNoRaw()) || (null != registeredNik
					&& !registeredNik.isEmpty() && !registeredNik.equals(bean.getIdNoRaw())))) {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.phonealreadyregistered",
						new Object[] { phone }, this.retrieveLocaleAudit(audit)),
						ReasonInvitationLink.INVALID_PHONE_NO);
			}
		}
		List<TrInvitationLink> invLinkByPhone = StringUtils.isBlank(phone) ? null
				: daoFactory.getInvitationLinkDao().getListInvitationByPhone(phone);
		if (null != invLinkByPhone && !invLinkByPhone.isEmpty() && "1".equals(invLinkByPhone.get(0).getIsActive())
				&& ((null != nik && !nik.isEmpty() && !nik.equals(invLinkByPhone.get(0).getIdNo()))
						|| (null != registeredNik && !registeredNik.isEmpty()
								&& !registeredNik.equals(invLinkByPhone.get(0).getIdNo())))) {
			throw new InvitationLinkException(messageSource.getMessage(MSG_EXISTING_PHONE,
					new Object[] { phone }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_PHONE_NO);
		}
	}

	private void checkEmail(String email, String emailRegex, String nik, String registeredNik, AuditContext audit) {
		if (StringUtils.isNotBlank(email) && !email.matches(emailRegex)) {
			throw new InvitationLinkException(messageSource.getMessage("businesslogic.invitationlink.invalidemail",
					new Object[] { email }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_EMAIL);
		}

		AmMsuser userByEmail = StringUtils.isBlank(email) ? null
				: userValidatorLogic.validateGetUserByEmail(email, false, audit);
		if (null != userByEmail) {
			PersonalDataBean bean = daoFactory.getUserDao().getUserDataByIdMsUser(userByEmail.getIdMsUser(), false);
			if ((null != nik && !nik.isEmpty() && !nik.equals(bean.getIdNoRaw()) || (null != registeredNik
					&& !registeredNik.isEmpty() && !registeredNik.equals(bean.getIdNoRaw())))) {
				throw new InvitationLinkException(messageSource.getMessage("businesslogic.user.emailalreadyregistered",
						new Object[] { email }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_EMAIL);
			}
		}
		List<TrInvitationLink> invLinkByEmail = StringUtils.isBlank(email) ? null
				: daoFactory.getInvitationLinkDao().getListInvitationByEmail(email);
		if (null != invLinkByEmail && !invLinkByEmail.isEmpty() && "1".equals(invLinkByEmail.get(0).getIsActive())
				&& ((null != nik && !nik.isEmpty() && !nik.equals(invLinkByEmail.get(0).getIdNo()))
						|| (null != registeredNik && !registeredNik.isEmpty()
								&& !registeredNik.equals(invLinkByEmail.get(0).getIdNo())))) {
			throw new InvitationLinkException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL,
					new Object[] { email }, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INVALID_EMAIL);
		}
	}
	
	private void updateRegisteredInvEmail(UpdateInvDataRequest request, TrInvitationLink invLink, MsVendor vendor,
			AuditContext audit) throws IOException {
		
		String validateMessage = "";
		
		if (StringUtils.isNumeric(request.getReceiverDetail())) {
			throw new InvitationLinkException(messageSource.getMessage(MSG_INVALID_RECEIVER_DETAIL,
					new Object[] { GlobalVal.INV_BY_EMAIL }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.INVALID_RECEIVER_DETAIL);
		}

		validateMessage = messageSource.getMessage(MSG_MISMATCH_RECEIVER_DETAIL,
				new Object[] { GlobalVal.INV_BY_EMAIL }, this.retrieveLocaleAudit(audit));
		commonValidatorLogic.validateMustEquals(request.getReceiverDetail(), request.getEmail(), validateMessage, StatusCode.MISMATCH_RECEIVER_DETAIL);

		AmMsuser user = getUserByReceiverDetail(invLink.getReceiverDetail(), audit);
		PersonalDataBean bean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		String oldEmail = invLink.getReceiverDetail();
		String oldRegisteredPhone = personalDataEncLogic.decryptToString(vru.getPhoneBytea());
		String oldPhone = invLink.getPhone();
		String newEmail = "";
		String newPhone = "";

		if (StringUtils.isBlank(oldPhone)) {
			invLink.setPhone(oldRegisteredPhone);
			oldPhone = oldRegisteredPhone;
		}

		if (!invLink.getReceiverDetail().equals(request.getReceiverDetail())) {
			newEmail = StringUtils.upperCase(request.getReceiverDetail());
		}

		if (StringUtils.isNotBlank(request.getPhone()) && !oldPhone.equals(request.getPhone())) {
			newPhone = request.getPhone();
		}

		this.checkPhoneEmailNik(newPhone, newEmail, null, bean.getIdNoRaw(), null, vendor.getVendorCode(), audit);

		if (StringUtils.isNotBlank(newEmail) && !invLink.getReceiverDetail().equals(newEmail)) {
			invLink.setReceiverDetail(newEmail);
			invLink.setEmail(newEmail);
		}
		if (StringUtils.isNotBlank(newPhone) && !oldPhone.equals(newPhone)) {
			invLink.setPhone(newPhone);
		}

		ChangeEmailPhoneRequestBean changeReq = new ChangeEmailPhoneRequestBean();
		changeReq.setNewEmail(request.getEmail());
		changeReq.setOldEmail(oldEmail);
		changeReq.setOldPhone(oldPhone);
		changeReq.setNewPhone(StringUtils.isNotBlank(request.getPhone()) ? request.getPhone() : oldPhone);
		ChangeEmailPhoneDigiRequest changeCompleteReq = new ChangeEmailPhoneDigiRequest();
		changeCompleteReq.setBean(changeReq);
		if (invLink.getMsVendor().getVendorCode().equalsIgnoreCase(GlobalVal.VENDOR_CODE_DIGISIGN)) {
			ChangeEmailPhoneDigiResponse response = digisignLogic.changeEmailPhoneDigi(changeCompleteReq,
					invLink.getMsTenant(), audit);
			if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(response.getBean().getResult())) {
				throw new DigisignException(response.getBean().getNotif());
			}
		}

		if (!oldEmail.equals(request.getReceiverDetail())) {
			user.setLoginId(invLink.getReceiverDetail());
			vru.setSignerRegisteredEmail(invLink.getReceiverDetail());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vru);
		}

		if (StringUtils.isNotBlank(request.getPhone()) && !oldRegisteredPhone.equals(request.getPhone())) {
			vru.setHashedSignerRegisteredPhone(MssTool.getHashedString(request.getPhone()));
			vru.setPhoneBytea(personalDataEncLogic.encryptFromString(invLink.getPhone()));
		}

		daoFactory.getUserDao().updateUser(user);
	}

	private AmMsuser getUserByReceiverDetail(String receiverDetail, AuditContext audit) {
		AmMsuser user = null;
		if (StringUtils.isNumeric(receiverDetail)) {
			user = userValidatorLogic.validateGetUserByPhone(receiverDetail, false, audit);
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(receiverDetail, false, audit);
		}

		return user;
	}

	private void updateRegisteredInvSms(UpdateInvDataRequest request, TrInvitationLink invLink, MsVendor vendor,
			AuditContext audit) throws IOException {
		if (!StringUtils.isNumeric(request.getReceiverDetail())) {
			throw new InvitationLinkException(messageSource.getMessage(MSG_INVALID_RECEIVER_DETAIL,
					new Object[] { PHONE }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.INVALID_RECEIVER_DETAIL);
		}

		if (!request.getReceiverDetail().equals(request.getPhone())) {
			throw new InvitationLinkException(messageSource.getMessage(MSG_MISMATCH_RECEIVER_DETAIL,
					new Object[] { PHONE }, this.retrieveLocaleAudit(audit)),
					ReasonInvitationLink.MISMATCH_RECEIVER_DETAIL);
		}

		AmMsuser user = getUserByReceiverDetail(invLink.getReceiverDetail(), audit);
		PersonalDataBean bean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		String oldPhone = personalDataEncLogic.decryptToString(vru.getPhoneBytea());

		if (!oldPhone.equals(request.getReceiverDetail())) {
			this.checkPhoneEmailNik(request.getReceiverDetail(), null, null, null, bean.getIdNoRaw(), vendor.getVendorCode(),
					audit);

			invLink.setReceiverDetail(request.getReceiverDetail());
			invLink.setPhone(request.getPhone());

			ChangeEmailPhoneRequestBean changeReq = new ChangeEmailPhoneRequestBean();
			changeReq.setNewEmail(user.getLoginId());
			changeReq.setOldEmail(user.getLoginId());
			changeReq.setOldPhone(oldPhone);
			changeReq.setNewPhone(request.getPhone());
			ChangeEmailPhoneDigiRequest changeCompleteReq = new ChangeEmailPhoneDigiRequest();
			changeCompleteReq.setBean(changeReq);
			if (invLink.getMsVendor().getVendorCode().equalsIgnoreCase(GlobalVal.VENDOR_CODE_DIGISIGN)) {
				ChangeEmailPhoneDigiResponse response = digisignLogic.changeEmailPhoneDigi(changeCompleteReq,
						invLink.getMsTenant(), audit);
				if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(response.getBean().getResult())) {
					throw new DigisignException(response.getBean().getNotif());
				}
			}

			user.setHashedPhone(MssTool.getHashedString(request.getPhone()));
			vru.setPhoneBytea(personalDataEncLogic.encryptFromString(invLink.getReceiverDetail()));
			vru.setHashedSignerRegisteredPhone(MssTool.getHashedString(request.getPhone()));
			daoFactory.getUserDao().updateUser(user);
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vru);
		}
	}
	
	/**
	 * 
	 * @param invLink TrInvitationLink
	 * @param check "reg" or any other string
	 * @param audit AuditContext
	 * @return 
	 * "1" IF check = "reg" AND ms_vendor_registered_user.is_registered = "1"<br>
	 * "1" IF check != "reg" AND ms_vendor_registered_user.is_active = "1"<br>
	 * "0" IF get am_msuser by phone / email returns null<br>
	 * "0" IF get ms_vendor_registered_user by id_ms_user and vendor_code returns null<br>
	 * to be continued
	 */
	private String checkInvRegisStatus(TrInvitationLink invLink, String check, AuditContext audit) {
		AmMsuser user = null;
		if (StringUtils.isNumeric(invLink.getReceiverDetail())) {
			user = userValidatorLogic.validateGetUserByPhoneAndVendorCode(invLink.getReceiverDetail(), false,
					invLink.getMsVendor().getVendorCode(), audit);
		} else {
			user = userValidatorLogic.validateGetUserByEmailAndVendorCode(invLink.getReceiverDetail(), false,
					invLink.getMsVendor().getVendorCode(), audit);
		}

		if (null == user) {
			return "0";
		}

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(
				user.getIdMsUser(), invLink.getMsVendor().getVendorCode());
		if (null == vendorUser) {
			return "0";
		}

		if ("reg".equals(check)) {
			return ("1".equals(vendorUser.getIsRegistered()) ? "1" : "0");
		} else {
			return ("1".equals(vendorUser.getIsActive()) ? "1" : "0");
		}
	}

	@Override
	public GenerateInvitationLinkForExpiredCertResponse generateInvLinkExpiredCert(
			GenerateInvitationLinkForExpiredCertRequest request, AuditContext audit) throws UnsupportedEncodingException {
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
		commonValidatorLogic.validateNotNull(docD, getMessage("businesslogic.document.docnotfounds", null, audit), StatusCode.DOCUMENT_NOT_FOUND_INV);
		
		TrDocumentH docH = docD.getTrDocumentH();
		
		MsVendor vendor = docD.getMsVendor();
		MsTenant tenant = docD.getMsTenant();
		
		if (!vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_VIDA)) {
			throw new InvitationLinkException(getMessage("businesslogic.invitationlink.genonlyforvendor", new Object[] {GlobalVal.VENDOR_CODE_VIDA}, audit), ReasonInvitationLink.MISMATCH_VENDOR);
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), true, audit);
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
		
		if (!userValidatorLogic.isCertifExpiredForSign(vru, audit)) {
			throw new InvitationLinkException(getMessage("businesslogic.invitationlink.activeecert", new Object[] {vru.getSignerRegisteredEmail()}, audit), ReasonInvitationLink.ACTIVE_ECERT);
		}
		
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationByEmailAndVendorCode(vru.getSignerRegisteredEmail(), vendor.getVendorCode());
		if (null == invLink) {
			invLink = daoFactory.getInvitationLinkDao().getInvitationByPhoneAndVendorCode(personalDataEncLogic.decryptToString(vru.getPhoneBytea()), vendor.getVendorCode());
		}
		
		if (null == invLink) {
			invLink = new TrInvitationLink();
			
			if ("1".equals(vru.getEmailService())) {
				invLink.setInvitationBy(GlobalVal.INV_BY_SMS);
				invLink.setReceiverDetail(personalDataEncLogic.decryptToString(vru.getPhoneBytea()));
			} else {
				invLink.setInvitationBy(GlobalVal.INV_BY_EMAIL);
				invLink.setReceiverDetail(vru.getSignerRegisteredEmail());
				invLink.setEmail(vru.getSignerRegisteredEmail());
			}
			
			
			invLink.setDtmCrt(new Date());
			invLink.setUsrCrt(audit.getCallerId());
			invLink.setPhone(personalDataEncLogic.decryptToString(vru.getPhoneBytea()));
			invLink.setAddress(personalData.getAddressRaw());
			invLink.setMsBusinessLine(docH.getMsBusinessLine());
			invLink.setBusinessLine(null != docH.getMsBusinessLine() ? docH.getMsBusinessLine().getBusinessLineCode() : null);
			invLink.setDateOfBirth(personalData.getUserPersonalData().getDateOfBirth());
			invLink.setFullName(user.getFullName());
			invLink.setGender(personalData.getUserPersonalData().getGender());
			invLink.setIdNo(personalData.getIdNoRaw());
			invLink.setIsActive("1");
			invLink.setIsRedirectUrl("1".equals(request.getIsEmbed()) ? "0" : "1");
			invLink.setKecamatan(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getKecamatan() : null);
			invLink.setKelurahan(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getKelurahan() : null);
			invLink.setKota(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getKota() : null);
			invLink.setMsOffice(docH.getMsOffice());
			invLink.setMsTenant(tenant);
			invLink.setMsVendor(vendor);
			invLink.setOffice(null != docH.getMsOffice() ? docH.getMsOffice().getOfficeCode() : null);
			invLink.setPlaceOfBirth(personalData.getUserPersonalData().getPlaceOfBirth());
			invLink.setProvinsi(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getProvinsi() : null);
			invLink.setRefNumber(docH.getRefNumber());
			invLink.setZipCode(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getZipcode() : null);
			invLink.setInvitationCode(generateInvitationCode());
			invLink.setIsEmbed(request.getIsEmbed());

			boolean useSyncPrivyVerif = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF);
			invLink.setUseSyncPrivyVerif(useSyncPrivyVerif ? "1" : "0");
			
			LOG.info("invitation with code {} inserted", invLink.getInvitationCode());
			daoFactory.getInvitationLinkDao().insertInvitationLink(invLink);
			
		} else {
			
			if (GlobalVal.INV_BY_EMAIL.equals(invLink.getInvitationBy())) {
				invLink.setEmail(vru.getSignerRegisteredEmail());
			}
			
			String oldCode = invLink.getInvitationCode();
			
			invLink.setDtmUpd(new Date());
			invLink.setUsrUpd(audit.getCallerId());
			invLink.setPhone(personalDataEncLogic.decryptToString(vru.getPhoneBytea()));
			invLink.setAddress(personalData.getAddressRaw());
			invLink.setMsBusinessLine(docH.getMsBusinessLine());
			invLink.setBusinessLine(null != docH.getMsBusinessLine() ? docH.getMsBusinessLine().getBusinessLineCode() : "");
			invLink.setDateOfBirth(personalData.getUserPersonalData().getDateOfBirth());
			invLink.setFullName(user.getFullName());
			invLink.setGender(personalData.getUserPersonalData().getGender());
			invLink.setIdNo(personalData.getIdNoRaw());
			invLink.setIsActive("1");
			invLink.setIsRedirectUrl("1".equals(request.getIsEmbed()) ? "0" : "1");
			invLink.setKecamatan(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getKecamatan() : null);
			invLink.setKelurahan(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getKelurahan() : null);
			invLink.setKota(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getKota() : null);
			invLink.setMsOffice(docH.getMsOffice());
			invLink.setMsTenant(tenant);
			invLink.setMsVendor(vendor);
			invLink.setOffice(null != docH.getMsOffice() ? docH.getMsOffice().getOfficeCode() : "");
			invLink.setPlaceOfBirth(personalData.getUserPersonalData().getPlaceOfBirth());
			invLink.setProvinsi(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getProvinsi() : null);
			invLink.setRefNumber(docH.getRefNumber());
			invLink.setZipCode(personalData.getUserPersonalData().getZipcodeBean() != null ? personalData.getUserPersonalData().getZipcodeBean().getZipcode() : null);
			invLink.setInvitationCode(generateInvitationCode());
			invLink.setIsEmbed(request.getIsEmbed());
			
			boolean useSyncPrivyVerif = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF);
			invLink.setUseSyncPrivyVerif(useSyncPrivyVerif ? "1" : "0");
			
			LOG.info("invitation link with code {} updated to {}", oldCode, invLink.getInvitationCode());
			daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		}
		
		GenerateInvitationLinkForExpiredCertResponse response = new GenerateInvitationLinkForExpiredCertResponse();
		String aesEncryptedLink = commonLogic.encryptMessageToString(invLink.getInvitationCode(), audit);
		String encrypted = URLEncoder.encode(aesEncryptedLink, StandardCharsets.UTF_8.toString());
		response.setLink(regInvLink + encrypted);
		
		return response;
	}

	@Override
	public GenerateInvitationLinkForExpiredCertResponse generateInvLinkExpiredCertEmbed(
			GenerateInvitationLinkForExpiredCertEmbedRequest request, AuditContext audit)
			throws UnsupportedEncodingException {
		EmbedMsgBeanV2 bean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		String docId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), bean.getMsTenant().getAesEncryptKey(), audit);
		
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(docId);
		if (!docD.getMsTenant().getTenantCode().equals(request.getTenantCode())) {
			throw new DocumentException(getMessage("businesslogic.document.docnotfoundintenant", new Object[] {docId, request.getTenantCode()}, audit), ReasonDocument.DOCUMENT_NOT_BELONG_TO_TENANT);
		}
		
		List<Map<String, Object>> signers = daoFactory.getDocumentDao().getSignerBeanByDocumentId(docId);
		Iterator<Map<String, Object>> itr = signers.iterator();
		
		while (itr.hasNext()) {
			Map<String,Object> signer = itr.next();
			if (signer.get("d1").equals(bean.getAmMsuser().getLoginId())) {
				break;
			}
			
			if (!signer.get("d1").equals(bean.getAmMsuser().getLoginId()) && !itr.hasNext()) {
				throw new DocumentException(getMessage("businesslogic.document.userisnotthesignerofthecontract2", new Object[] {"Document Id ", docId}, audit), ReasonDocument.USER_IS_NOT_THE_SIGNER_OF_THE_CONTRACT);
			}
		}
		
		GenerateInvitationLinkForExpiredCertRequest passedReq = new GenerateInvitationLinkForExpiredCertRequest();
		passedReq.setDocumentId(docId);
		passedReq.setLoginId(bean.getAmMsuser().getLoginId());
		passedReq.setIsEmbed("1");
		
		return generateInvLinkExpiredCert(passedReq, audit);
	}
}
