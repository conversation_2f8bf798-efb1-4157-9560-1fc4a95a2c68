package com.adins.esign.businesslogic.impl.embed;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.mail.MessagingException;
import javax.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.ExcelLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.businesslogic.api.embed.DocumentEmbedLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.CancelBalanceMutationBean;
import com.adins.esign.model.custom.CheckDocumentBeforeSigningBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.InquiryDocumentBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.ViewSignerListBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.DocumentValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.BulkSignDocumentEmbedRequest;
import com.adins.esign.webservices.model.BulkSignDocumentRequest;
import com.adins.esign.webservices.model.BulkSignDocumentResponse;
import com.adins.esign.webservices.model.CancelAgreementRequest;
import com.adins.esign.webservices.model.CancelAgreementResponse;
import com.adins.esign.webservices.model.CheckDocumentSendStatusEmbedRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusResponse;
import com.adins.esign.webservices.model.DocumentExcelReportResponse;
import com.adins.esign.webservices.model.ListInquiryDocumentEmbedRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentRequest;
import com.adins.esign.webservices.model.ResendSignNotificationResponse;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.esign.webservices.model.SignDocumentRequest;
import com.adins.esign.webservices.model.SignDocumentResponse;
import com.adins.esign.webservices.model.SignerListRequest;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedRequest;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedResponse;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.ResendSignNotificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Request;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Response;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedRequest;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedResponse;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedRequest;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmailException;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.SendNotificationException;
import com.adins.exceptions.SendNotificationException.ReasonSendNotif;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmailException.ReasonEmail;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.util.Tool;
import com.google.gson.Gson;

@Component
public class GenericDocumentEmbedLogic extends BaseLogic implements DocumentEmbedLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericDocumentEmbedLogic.class);
	
	@Value("${esign.sign.uri}") private String linkTtdEsign;
	@Value("${spring.mail.username}") private String fromEmailAddr;
	
	private static final String MAP_KEY_EMAIL = "email";
	private static final String MAP_KEY_FULLNAME = "fullname";
	private static final String MAP_KEY_TENANT = "tenant";
	
	private static final String ERROR = " error ";
	
	// Status Aktivasi Signer
	private static final String USER_STATUS_NOT_REGISTERED = "Belum Registrasi";
	private static final String USER_STATUS_NOT_ACTIVATED = "Belum Aktivasi";
	private static final String USER_STATUS_ACTIVATED = "Sudah Aktivasi";
	private static final String USER_STATUS_UNKNOWN = "Unknown";
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private ExcelLogic excelLogic;
	@Autowired private SmsLogic smsLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private Gson gson;
	@Autowired private DocumentValidatorLogic documentValidatorLogic;
	@Autowired private WhatsAppLogic whatsAppLogic;
	@Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;
	@Autowired private JatisSmsLogic jatisSmsLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	@Autowired private MessageDeliveryReportLogic messageDeliveryReportLogic;

	private final Set<String> resendNotifSignSet = ConcurrentHashMap.newKeySet();
	
	@Override
	public CheckDocumentBeforeSigningEmbedResponse checkDocumentBeforeSigningEmbed(
			CheckDocumentBeforeSigningEmbedRequest request, AuditContext audit) {
		
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		
		List<String> listDocIdEncrypt = request.getListDocumentId();
		List<String> listDocIdDecrypt = new ArrayList<>();
		
		for (String encryptedId : listDocIdEncrypt) {
			try {
				String documentId = commonLogic.decryptMessageToString(encryptedId, msg.getMsTenant().getAesEncryptKey(), audit);
				listDocIdDecrypt.add(documentId);
			} catch (Exception e) {
				throw new EmbedMsgException(getMessage("businesslogic.embedmsg.invalidencryptedocument", null, audit), e, ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
			}
		}
		
		String[] listDocumentIdDecrypt = listDocIdDecrypt.toArray(new String[listDocIdDecrypt.size()]);
		
		TrDocumentD compareDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentIdDecrypt[0]);
		String compareVendorCode = compareDocD.getMsVendor().getVendorCode();
		
		List<CheckDocumentBeforeSigningBean> listCheckDocumentBeforeSigning = new ArrayList<>();
		
		
		for (int i = 0; i < listDocIdDecrypt.size(); i++) {
			if (StringUtils.isBlank(listDocumentIdDecrypt[i])) {
				throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
						ReasonDocument.EMPTY_DOCUMENT_ID);
			}
			
			TrDocumentD trDocumentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentIdDecrypt[i]);
			if (trDocumentD == null) {
				throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, new Object[]{listDocumentIdDecrypt[i]}, audit),
	                    ReasonDocument.DOCUMENT_NOT_FOUND);
	        }
			
			if (!compareVendorCode.equals(trDocumentD.getMsVendor().getVendorCode())) {
				throw new DocumentException(
						getMessage("businesslogic.document.documentvendornotmatch", null, audit),
						ReasonDocument.INVALID_DOCUMENT_VENDOR);
			}
			
			CheckDocumentBeforeSigningBean bean = new CheckDocumentBeforeSigningBean();
			bean.setDocumentId(listDocIdEncrypt.get(i));
			bean.setIsCurrentTopPriority(documentValidatorLogic.isDocumentTopPriorityForSigner(trDocumentD, msg.getAmMsuser()) ? "1" : "0");
			
			listCheckDocumentBeforeSigning.add(bean);
		}
		List<CheckDocumentBeforeSigningBean> listSigningProcess = null;
		List<CheckDocumentBeforeSigningBean> listSigningUser = null;
		
		if(compareVendorCode.equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			listSigningUser = daoFactory.getDocumentDao().getListDocIdAndSigningProcessPrivy(listDocumentIdDecrypt, msg.getAmMsuser().getIdMsUser());
			listSigningProcess = daoFactory.getDocumentDao().getListCheckDocumentBeforeSigningFromTrDocumentH(listDocumentIdDecrypt);
		}else {
			listSigningProcess =  daoFactory.getDocumentDao().getListCheckDocumentBeforeSigning(listDocumentIdDecrypt);
			listSigningUser =  daoFactory.getDocumentDao().getListCheckDocumentBeforeSigningEmbed(listDocumentIdDecrypt, msg.getAmMsuser().getIdMsUser());
			
		}
		
		
		for (CheckDocumentBeforeSigningBean bean : listCheckDocumentBeforeSigning) {
			String decryptedDocumentId = bean.getDocumentId();
			
			for (CheckDocumentBeforeSigningBean signingProcess : listSigningProcess) {
				String encryptedDocumentId = commonLogic.encryptMessageToString(signingProcess.getDocumentId(), msg.getMsTenant().getAesEncryptKey(), audit);
				if(CollectionUtils.isEmpty(listSigningUser)) {
					if (encryptedDocumentId.equals(decryptedDocumentId)) {
						TrDocumentD trDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(commonLogic.decryptMessageToString(bean.getDocumentId(), msg.getMsTenant().getAesEncryptKey(), audit));
						AmMsuser amMsUser = msg.getAmMsuser();
						if ("1".equals(trDocD.getIsSequence())) {
							boolean statusSign =  documentValidatorLogic.isSignSequenceValid(trDocD, amMsUser, audit);
							if (!statusSign) {
								bean.setSigningProcess("2");
								break;
							}
						}
						bean.setSigningProcess("0");
						break;
					}
				
				} else {
					for (CheckDocumentBeforeSigningBean signingUser : listSigningUser) {
						if (encryptedDocumentId.equals(decryptedDocumentId)) {
							if (signingProcess.getSigningProcess().equals("1") && signingUser.getSigningProcess().equals("1")) {
								bean.setSigningProcess(signingProcess.getSigningProcess());
								break;
							}else if ( signingProcess.getSigningProcess().equals("0"))  {
								TrDocumentD trDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(commonLogic.decryptMessageToString(bean.getDocumentId(), msg.getMsTenant().getAesEncryptKey(), audit));
								AmMsuser amMsUser = daoFactory.getUserDao().getUserByLoginId(msg.getAmMsuser().getLoginId());
								if ( trDocD.getIsSequence().equals("1")) {
										boolean statusSign =  documentValidatorLogic.isSignSequenceValid(trDocD, amMsUser, audit);
										if (!statusSign)	{
											bean.setSigningProcess("2");
											break;
										}
								}
							}
							bean.setSigningProcess("0");
							break;
						}
					}
				}
			}
		}
		
		MsVendorRegisteredUser vendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(msg.getAmMsuser().getIdMsUser(), compareDocD.getMsVendor().getVendorCode());
		boolean mustScrollToSign = tenantSettingsLogic.getSettingValue(compareDocD.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_MUST_SCROLL_TO_SIGN);

	    CheckDocumentBeforeSigningEmbedResponse response = new CheckDocumentBeforeSigningEmbedResponse();
	    
		response.setCertificateActiveStatus("1");
		response.setMustScrollToSignStatus(mustScrollToSign ? "1" : "0");
		if (userValidatorLogic.isCertifExpiredForSign(vendorRegisteredUser, audit)) {
			response.setCertificateActiveStatus("0");
		}
		
	    response.setListCheckDocumentBeforeSigning(listCheckDocumentBeforeSigning);
	    Status status = new Status();
		status.setCode(0);
		status.setMessage("Success");
		response.setStatus(status);
	    return response;
	}


	@Override
	public ViewDocumentResponse getDocumentFileEmbed(ViewDocumentRequest request, AuditContext audit) {
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		MsTenant tenant = msg.getMsTenant();
		
		String decryptedDocumentId = commonLogic.decryptMessageToString(request.getDocumentId(), tenant.getAesEncryptKey(), audit);
		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(decryptedDocumentId);
		if (null == document) {
			throw new DocumentException(getMessage("businesslogic.document.docnotfounds", new String[] {decryptedDocumentId}, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}

		if (!document.getMsTenant().getTenantCode().equals(tenant.getTenantCode())) {
			throw new DocumentException(getMessage("businesslogic.document.tenantcannotaccessdoc", null, audit),
					ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
		}

		// Isi audit dengan salah satu penanda tangan
		List<Map<String, Object>> documentSignerList = daoFactory.getDocumentDao().getDocumentSignerList(decryptedDocumentId);
		if (CollectionUtils.isEmpty(documentSignerList)) {
			audit.setCallerId(GlobalVal.CALLER_ID_MONITORING);
		} else {
			Map<String, Object> map = documentSignerList.get(0);
			audit.setCallerId((String) map.get("d0"));
		}
		
		ViewDocumentRequest viewRequest = new ViewDocumentRequest();
		viewRequest.setDocumentId(decryptedDocumentId);
		viewRequest.setTenantCode(tenant.getTenantCode());
		return documentLogic.viewDocumentWithoutSecurity(viewRequest, audit);
	}

	@Override
	public CancelDigitalSignEmbedResponse cancelDigitalSign(CancelDigitalSignEmbedRequest request, AuditContext audit) {
		CancelDigitalSignEmbedResponse response = new CancelDigitalSignEmbedResponse();
		EmbedMsgBeanV2 embedBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), embedBean.getMsTenant().getAesEncryptKey(), audit);
		audit.setCallerId(embedBean.getDecryptedEmail());
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocIdNewTran(documentId);
		if (null == docD) {
			throw new DocumentException(
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, new String[] { documentId } , retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}

		TrDocumentH docH = docD.getTrDocumentH();
		
		if (!docH.getMsTenant().getTenantCode().toUpperCase().equals(request.getTenantCode().toUpperCase())) {
			throw new DocumentException(
					messageSource.getMessage("businesslogic.document.documentnotfoundintenant", new String[] { docD.getDocumentId(), request.getTenantCode()} , retrieveLocaleAudit(audit)),
					ReasonDocument.CANNOT_ACCESS_OTHER_TENANT);
		}
		
		if (!"1".equals(docH.getIsActive())) {
			throw new DocumentException(getMessage("businesslogic.document.inactiveagreement",
					new String[] { docH.getRefNumber()}, audit), ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}
		
		docH.setIsActive("0");
		docH.setUsrUpd(audit.getCallerId());
		docH.setDtmUpd(new Date());
		daoFactory.getDocumentDao().updateDocumentH(docH);

		if (StringUtils.isNotBlank(docH.getResultUrl())) {
			Status status = this.callUrlCancel(docH.getResultUrl());
			response.setStatus(status);

//			revert saldo sudah tidak digunakan
//			CancelAgreementRequest cancelAgreementRequest = new CancelAgreementRequest();
//			cancelAgreementRequest.setAgreementNo(docH.getRefNumber());
//			CancelAgreementResponse cancelAgreementResponse = this.cancelAgreement(cancelAgreementRequest, audit);
//			response.setStatus(cancelAgreementResponse.getStatus());
		}
		
		Status status = new Status();
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		response.setStatus(status);

		return response;
	}
	
	private Status callUrlCancel(String url) {
		String completedUrl = url + "&isSuccess=false";
		WebClient client = WebClient.create(completedUrl);
		MssTool.trustAllSslCertificate(client);
		Response response = client.post(null);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String result = null;
		try {
			result = IOUtils.toString(isReader);
			result = result.replace("\\", "");
			result = result.substring(1, result.length() - 1);
		} catch (Exception e) {
			LOG.error("Gagal read response callback Core System.", e);
		}

		LOG.info("Cancelling with URL {} response: {}", completedUrl, result);
		return gson.fromJson(result, Status.class);
	}

	private CancelAgreementResponse cancelAgreement(CancelAgreementRequest request, AuditContext audit) {
		List<CancelBalanceMutationBean> balanceMutation = daoFactory.getBalanceMutationDao()
				.getListBalanceMutationByRefNo(request.getAgreementNo());
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());

		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_CNCL);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(request.getAgreementNo());
		MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS,
				GlobalVal.CODE_LOV_SDT_AVAILABLE);

		CancelAgreementResponse response = new CancelAgreementResponse();
		Status status = new Status();

		BigInteger idSdtPrev = null;
		for (CancelBalanceMutationBean bean : balanceMutation) {
			if (bean.getBalanceType().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT)) {
				TrStampDuty stampDuty = daoFactory.getStampDutyDao()
						.getStampDutyById(bean.getIdStampDuty().longValue());
				Object latestBM = daoFactory.getStampDutyDao()
						.getLatestStampDutyDetailTrxType(bean.getIdStampDuty().longValue());
				if (!latestBM.equals(GlobalVal.CODE_LOV_TRX_TYPE_CNCL)) {
					TrDocumentD docD = daoFactory.getDocumentDao()
							.getDocumentDetailById(bean.getIdDocumentD().longValue());
					String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
					saldoLogic.insertBalanceMutation(stampDuty, docH, docD, balanceType, trxType, docH.getMsTenant(),
							docD.getMsVendor(), new Date(), request.getAgreementNo(), 1, trxNo, user, null, null,
							audit);

					stampDuty.setMsLov(sdtStatus);
					stampDuty.setDtmUpd(new Date());
					stampDuty.setUsrUpd(audit.getCallerId());
					daoFactory.getStampDutyDao().updateTrStampDuty(stampDuty);
				} else if (bean.getIdStampDuty() != idSdtPrev) {
					status.setCode(200);
					status.setMessage(this.messageSource.getMessage("businesslogic.document.alreadycanceled", null,
							this.retrieveLocaleAudit(audit)));
					response.setStatus(status);
					return response;
				}
			}
			idSdtPrev = bean.getIdStampDuty();
		}

		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);

		return response;
	}
	
	@Override
	public SignDocumentEmbedV2Response signDocument(SignDocumentEmbedV2Request request, AuditContext audit) {
		
		EmbedMsgBeanV2 msgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		audit.setCallerId(msgBean.getDecryptedEmail());
		
		MsTenant tenant = msgBean.getMsTenant();
		String decryptedDocumentId = commonLogic.decryptMessageToString(request.getDocumentId(), tenant.getAesEncryptKey(), audit);
		
		SignDocumentRequest signRequest = new SignDocumentRequest();
		signRequest.setDocumentId(decryptedDocumentId);
		signRequest.setEmail(msgBean.getDecryptedEmail());
		
		SignDocumentResponse signResponse = documentLogic.signDocumentWithoutSecurity(signRequest, audit);
		
		// Encrypt semua documentId yang ada di object docs
		if (CollectionUtils.isNotEmpty(signResponse.getDocs())) {
			for (int i = 0; i < signResponse.getDocs().size(); i++) {
				String documentId = (String) signResponse.getDocs().get(i).get("documentId");
				String encryptedDocumentId = commonLogic.encryptMessageToString(documentId, tenant.getAesEncryptKey(), audit);
				signResponse.getDocs().get(i).put("documentId", encryptedDocumentId);
			}
		}
		
		SignDocumentEmbedV2Response response = new SignDocumentEmbedV2Response();
		response.setUrl(signResponse.getUrl());
		response.setRegister(signResponse.getRegister());
		response.setVendorCode(signResponse.getVendorCode());
		response.setDocs(signResponse.getDocs());
		response.setStatus(signResponse.getStatus());
		return response;
	}
	
	private String getInquiryRegionCodeEmbedV2(ListInquiryDocumentEmbedRequest request, String tenantkey, AuditContext audit) {
		String regionCode = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(request.getRegionCode())) {
			regionCode = commonLogic.decryptMessageToString(request.getRegionCode(), tenantkey, audit);
		}
		return regionCode;
	}
	
	private String getInquiryOfficeCodeEmbedV2(String officeCode, ListInquiryDocumentEmbedRequest request, String tenantkey, AuditContext audit) {
		
		if (!"1".equals(request.getIsHO())) {
			return officeCode;
		}
		
		// isHO = 1
		if (StringUtils.isBlank(request.getOfficeCode())) {
			return StringUtils.EMPTY;
		}
		
		return commonLogic.decryptMessageToString(request.getOfficeCode(), tenantkey, audit);
	}
	
	@Override
	public DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request, AuditContext audit) {
		
		EmbedMsgBeanV2 msgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		MsTenant tenant = msgBean.getMsTenant();
		String aesKey = tenant.getAesEncryptKey();
		audit.setCallerId(msgBean.getDecryptedEmail());

		String officeCode = this.getInquiryOfficeCodeEmbedV2(msgBean.getMsOffice().getOfficeCode(), request, aesKey, audit);
		String regionCode = this.getInquiryRegionCodeEmbedV2(request, aesKey, audit);

		ListInquiryDocumentRequest listInquiryDocumentRequest = new ListInquiryDocumentRequest();
		listInquiryDocumentRequest.setIsMonitoring(request.isMonitoring());
		listInquiryDocumentRequest.setTenantCode(tenant.getTenantCode());
		listInquiryDocumentRequest.setOfficeCode(officeCode);
		listInquiryDocumentRequest.setRegionCode(regionCode);
		listInquiryDocumentRequest.setCustomerName(request.getCustomerName());
		listInquiryDocumentRequest.setRefNumber(request.getRefNumber());
		listInquiryDocumentRequest.setRequestedDateStart(request.getRequestedDateStart());
		listInquiryDocumentRequest.setRequestedDateEnd(request.getRequestedDateEnd());
		listInquiryDocumentRequest.setCompletedDateStart(request.getCompletedDateStart());
		listInquiryDocumentRequest.setCompletedDateEnd(request.getCompletedDateEnd());
		listInquiryDocumentRequest.setIsActive(request.getIsActive());

		MsLov lovDocType = new MsLov();
		if (StringUtils.isNotBlank(request.getDocType())) {
			lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
					request.getDocType());
			if (null == lovDocType) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { "Document type", request.getDocType() }, this.retrieveLocaleAudit(audit)),
						ReasonDocument.DOC_TYPE_NOT_EXIST);
			}
		}

		MsLov lovSignStatus = new MsLov();
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
					request.getTransactionStatus());
			if (null == lovSignStatus) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { "Transaction status", request.getTransactionStatus() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.SIGN_STATUS_NOT_EXISTS);
			}
		}
		if (!isDateRangeValid(request.getRequestedDateStart(), request.getRequestedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { "Request" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		if (!isDateRangeValid(request.getCompletedDateStart(), request.getCompletedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { "Completed" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		String[] paramsInquiry = new String[15];
		this.populateParamsInquiry(paramsInquiry, listInquiryDocumentRequest, lovSignStatus, lovDocType, 0, 0);

		List<Map<String, Object>> docList = daoFactory.getDocumentInquiryDao().getListInquiryDocumentMonitoring(paramsInquiry, true);
		List<InquiryDocumentBean> documentBeanList = this.convertInquiryEmbedMapsToBeans(docList, false, true, "", audit);

		DocumentExcelReportResponse response = new DocumentExcelReportResponse();
		response.setBase64ExcelReport(
				this.generateDocumentExcelFile(documentBeanList, tenant.getRefNumberLabel(), audit));
		response.setFilename(this.generateDocumentExcelFilename(request));
		return response;
	}

	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao()
				.getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);

		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}

		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}
	
	private void populateParamsInquiry(String[] paramsInquiry, ListInquiryDocumentRequest request, MsLov lovSignStatus,
			MsLov lovDocType, int start, int end) {
		paramsInquiry[0] = request.getTenantCode();
		paramsInquiry[1] = request.getOfficeCode();
		paramsInquiry[2] = StringUtils.upperCase(request.getCustomerName());
		paramsInquiry[3] = 0 == lovSignStatus.getIdLov() ? "" : String.valueOf(lovSignStatus.getIdLov()); // jika lov
																											// null maka
																											// idLov
																											// terisi 0
		paramsInquiry[4] = request.getRefNumber();
		paramsInquiry[5] = request.getRequestedDateStart();
		paramsInquiry[6] = request.getRequestedDateEnd();
		paramsInquiry[7] = request.getCompletedDateStart();
		paramsInquiry[8] = request.getCompletedDateEnd();
		paramsInquiry[9] = String.valueOf(start);
		paramsInquiry[10] = String.valueOf(end);
		paramsInquiry[11] = 0 == lovDocType.getIdLov() ? "" : String.valueOf(lovDocType.getIdLov());
		paramsInquiry[12] = request.getRegionCode();
		
		if (paramsInquiry.length >= 15) {
			paramsInquiry[14] = request.getIsActive();
		}
	}
	
	private List<InquiryDocumentBean> convertInquiryEmbedMapsToBeans(List<Map<String, Object>> documentList,
			boolean isInquiry, boolean isEncrypt, String aesKey, AuditContext audit) {
		Iterator<Map<String, Object>> itr = documentList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			InquiryDocumentBean bean = new InquiryDocumentBean();
			bean.setRefNumber((String) map.get("d1"));
			bean.setDocTypeName((String) map.get("d2"));
			bean.setDocTemplateName((String) map.get("d3"));
			bean.setCustomerName((String) map.get("d4"));
			bean.setRequestDate((String) map.get("d5"));
			bean.setCompleteDate((String) map.get("d6"));
			
			if (isInquiry) {
				if(isEncrypt && StringUtils.isNotBlank(aesKey)) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), aesKey, audit));
				} else if (isEncrypt) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), audit));
				} else {
					bean.setDocumentId((String) map.get("d7"));
				}
			}
			bean.setTotalSigned(((String) map.get("d8")));
			bean.setSignStatus((String) map.get("d12"));
			bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
			bean.setOfficeName((String) map.get("d10"));
			bean.setRegionName((String) map.get("d11"));
			bean.setTotalStamped((String) map.get("d13"));
			bean.setStatusOtomatisStamping((String) map.get("d14"));

			// status proses materai
			short status = (short) map.get("d15");
			if (0 == status) {
				bean.setStatusProsesMaterai(GlobalVal.HASIL_STAMPING_NOT_STARTED);
			} else if (1 == status || 51 == status || 321 == status || 521 == status) {
				bean.setStatusProsesMaterai(GlobalVal.HASIL_STAMPING_FAILED);
			} else if (2 == status || 52 == status || 322 == status || 522 == status || 5 == status || 55 == status
					|| 325 == status || 525 == status) {
				bean.setStatusProsesMaterai(GlobalVal.HASIL_STAMPING_IN_PROGRESS);
			} else if (3 == status || 53 == status || 323 == status || 523 == status) {
				bean.setStatusProsesMaterai(GlobalVal.HASIL_STAMPING_SUCCESS);
			} else {
				bean.setStatusProsesMaterai(USER_STATUS_UNKNOWN);
			}
			bean.setVendorCode((String) map.get("d16"));
			if ("1".equals(map.get("d17"))) {
				bean.setSigningProcess("Proses TTD");
			} else {
				bean.setSigningProcess("Belum TTD");
			}
			bean.setCanStartStamp("1");
			
			String isActiveMap = (String) map.get("d18");
			
			if (isActiveMap.equals("1")) {
				bean.setIsActive("Active");
			} else {
				bean.setIsActive("Not Active");
			}
			
			documentBeanList.add(bean);
		}
		return documentBeanList;
	}
	
	private String generateDocumentExcelFilename(ListInquiryDocumentEmbedRequest request) {
		StringBuilder filename = new StringBuilder();
		filename.append("DOCUMENT_REPORT");
		if (StringUtils.isNotBlank(request.getCustomerName())) {
			filename.append("_").append(request.getCustomerName());
		}
		if (StringUtils.isNotBlank(request.getRefNumber())) {
			filename.append("_").append(request.getRefNumber());
		}
		if (StringUtils.isNotBlank(request.getDocType())) {
			filename.append("_").append(request.getDocType());
		}
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			filename.append("_").append(request.getTransactionStatus());
		}
		if (StringUtils.isNotBlank(request.getRegionCode())) {
			filename.append("_").append(request.getRegionCode());
		}
		if (StringUtils.isNotBlank(request.getOfficeCode())) {
			filename.append("_").append(request.getOfficeCode());
		}
		filename.append("_").append(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT_SDT_REPORT));
		filename.append(".xlsx");
		return filename.toString();
	}

	private String generateDocumentExcelFile(List<InquiryDocumentBean> listDocument, String refNoLabel,
			AuditContext audit) {
		byte[] excelByteArray = null;
		try {
			excelByteArray = excelLogic.generateEmbed((workbook, styleBoldText) -> this
					.createDocumentExcelSheet(workbook, styleBoldText, listDocument, refNoLabel));
		} catch (Exception e) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.generateexcelerror", null,
					this.retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_EXCEL_ERROR);
		}
		return Base64.getEncoder().encodeToString(excelByteArray);
	}
	
	private void createDocumentExcelSheet(XSSFWorkbook workbook, XSSFCellStyle styleBoldText,
			List<InquiryDocumentBean> listDocument, String refNoLabel) {
		XSSFSheet mainSheet = workbook.createSheet("Laporan Dokumen");

		// Untuk Monitoring HO
		String[] monitoringHODocumentColumn = new String[] { refNoLabel, "Tipe Dok", "Nama Dok", "Nama Pelanggan",
				"Tanggal Permintaan", "Tanggal Selesai", "Proses TTD", "Total Meterai", "Status TTD", "Status", "Office", "Region" };

		for (int i = 0; i < monitoringHODocumentColumn.length; i++) {
			if (i == 2) {
				mainSheet.setColumnWidth(i, 11000);
			} else {
				mainSheet.setColumnWidth(i, 6000);
			}
		}

		XSSFRow rowOne = mainSheet.createRow(0);
		for (int i = 0; i < monitoringHODocumentColumn.length; i++) {
			XSSFCell cell = rowOne.createCell(i);
			cell.setCellValue(monitoringHODocumentColumn[i]);
			cell.setCellStyle(styleBoldText);
		}

		// 11 columnms
		for (int i = 0; i < listDocument.size(); i++) {
			XSSFRow row = mainSheet.createRow(i + 1);
			XSSFCell cellOne = row.createCell(0);
			XSSFCell cellTwo = row.createCell(1);
			XSSFCell cellThree = row.createCell(2);
			XSSFCell cellFour = row.createCell(3);
			XSSFCell cellFive = row.createCell(4);
			XSSFCell cellSix = row.createCell(5);
			XSSFCell cellSeven = row.createCell(6);
			XSSFCell cellEight = row.createCell(7);
			XSSFCell cellNine = row.createCell(8);
			XSSFCell cellTen = row.createCell(9);
			XSSFCell cellEleven = row.createCell(10);
			XSSFCell cellTwelve = row.createCell(11);

			InquiryDocumentBean bean = listDocument.get(i);
			cellOne.setCellValue(bean.getRefNumber());
			cellTwo.setCellValue(bean.getDocTypeName());
			cellThree.setCellValue(bean.getDocTemplateName());
			cellFour.setCellValue(bean.getCustomerName());
			cellFive.setCellValue(bean.getRequestDate());
			cellSix.setCellValue(bean.getCompleteDate());
			cellSeven.setCellValue(bean.getTotalSigned());
			cellEight.setCellValue(bean.getTotalStamped());
			cellNine.setCellValue(bean.getSignStatus());
			cellTen.setCellValue(bean.getIsActive());
			cellEleven.setCellValue(bean.getOfficeName());
			cellTwelve.setCellValue(bean.getRegionName());

		}
	}


	@Override
	public ResendSignNotificationResponse resendSignNotification(ResendSignNotificationEmbedRequest request,
			AuditContext audit) {
		EmbedMsgBeanV2 msgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), msgBean.getMsTenant().getAesEncryptKey(), audit);
		audit.setCallerId(msgBean.getDecryptedEmail());

		validateResendNotifSignConcurrently(documentId, audit);

		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

		List<Map<String, Object>> signerList = daoFactory.getDocumentDao().getDocumentSignerList(documentId);
		Iterator<Map<String, Object>> itr = signerList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			String name = (String) map.get("d2");
			String email = (String) map.get("d0");
			String signStatus = (String) map.get("d4");

			this.resendSignRequest(document, email, name, signStatus, audit);
		}

		resendNotifSignSet.remove(documentId);

		return new ResendSignNotificationResponse();
	}
	
	private String generateSignLink(String docId) {
		StringBuilder link = new StringBuilder().append(linkTtdEsign).append(docId);
		return link.toString();
	}
	
	private void resendSignRequest(TrDocumentD document, String email, String name, String signStatus,
			AuditContext audit) {
		if (!"Signed".equalsIgnoreCase(signStatus)) {

			MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(document.getMsTenant().getTenantCode());
			String link = this.generateSignLink(document.getDocumentId());
			MsMsgTemplate template;
			Map<String, Object> templateParameters = new HashMap<>();
			MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, document.getMsVendor().getVendorCode());
			NotificationType notifType = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.RESEND_SIGN_NOTIF_EMBED_V2, msVendorRegisteredUser.getEmailService());
			List<TrDocumentD> docDs = new ArrayList<>();
			docDs.add(document);
			MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_RESEND_SIGN_NOTIF);
			MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_REQUEST_SIGN);
			
			SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
			auditTrailBean.setDocumentDs(docDs);
			auditTrailBean.setEmail(email);
			auditTrailBean.setLovProcessType(processType);
			auditTrailBean.setLovSendingPoint(sendingPoint);
			auditTrailBean.setPhone(personalDataEncLogic.decryptToString(msVendorRegisteredUser.getPhoneBytea()));
			auditTrailBean.setTenant(tenant);
			auditTrailBean.setUser(msVendorRegisteredUser.getAmMsuser());
			auditTrailBean.setVendorPsre(document.getMsVendor());
			
			if (notifType != NotificationType.EMAIL) {
				TrDocumentH docH = document.getTrDocumentH();
				String phoneNumber = personalDataEncLogic.decryptToString(msVendorRegisteredUser.getPhoneBytea());
				validateNotificationLimitByPeriod(docH, sendingPoint, tenant, phoneNumber, audit);
				validateNotificationDailyAttempt(docH, tenant, audit);
			}
			
			if (NotificationType.WHATSAPP == notifType) {
				resendSignRequestWa(tenant, templateParameters, email, document, auditTrailBean, audit);
			} else if (NotificationType.WHATSAPP_HALOSIS == notifType) {
				resendSignRequestWaHalosis(tenant, templateParameters, email, document, auditTrailBean, audit);
			}
			else if (NotificationType.SMS_VFIRST == notifType) {
				resendSignRequestSms(tenant, link, templateParameters, email, document, auditTrailBean, audit);
			} else if (NotificationType.SMS_JATIS == notifType) {
				resendSignRequestSmsJatis(tenant, link, templateParameters, email, document, auditTrailBean, audit);
			} else {
				Map<String, Object> userMap = new HashMap<>();
				userMap.put(MAP_KEY_FULLNAME, name);
				userMap.put(MAP_KEY_TENANT, tenant.getTenantName());
				userMap.put(MAP_KEY_EMAIL, email);
				userMap.put("link", link);
				templateParameters.put("user", userMap);

				template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD, templateParameters);
				EmailInformationBean emailInfo = new EmailInformationBean();
				emailInfo.setFrom(fromEmailAddr);
				emailInfo.setTo(new String[] { email });
				emailInfo.setSubject(template.getSubject());
				emailInfo.setBodyMessage(template.getBody());
				try {
					emailSenderLogic.sendEmail(emailInfo, null, auditTrailBean);
					LOG.info("Resend sign request via Email Sent.");
				} catch (MessagingException e) {
					throw new EmailException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMAIL_SENDER, null,
							this.retrieveLocaleAudit(audit)), ReasonEmail.SEND_EMAIL_ERROR);
				}
			}
				
			
			
		}
	}
	
	private void resendSignRequestWa(MsTenant tenant, Map<String, Object> templateParameters, String email, TrDocumentD document, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);
		TrDocumentH docH = document.getTrDocumentH();
		Map<String, Object> docMap = new HashMap<>();
		docMap.put("refNumber", docH.getRefNumber());
		
		templateParameters.put("doc", docMap);
		
		String buttonText = document.getDocumentId();
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent("sign_link_invitation_without_password", templateParameters);
		
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(tenant.getTenantName());
		bodyTexts.add(document.getDocumentId());
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(),
						document.getMsVendor().getVendorCode());

		String phoneNumber = personalDataEncLogic.decryptToString(vuser.getPhoneBytea());
		
		String reservedTrxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		
		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setBodyTexts(bodyTexts);
		request.setButtonText(buttonText);
		request.setMsTenant(tenant);
		request.setTemplate(template);
		request.setAmMsuser(user);
		request.setReservedTrxNo(reservedTrxNo);
		request.setPhoneNumber(phoneNumber);
		request.setTrDocumentH(docH);
		request.setMsBusinessLine(docH.getMsBusinessLine());
		request.setMsOffice(docH.getMsOffice());
		request.setRefNo(docH.getRefNumber());
		request.setNotes(phoneNumber + " : Resend WhatsApp Sign Request");
		
		if ("1".equals(gs.getGsValue())) {
			whatsAppLogic.sendMessage(request, auditTrailBean, audit);
			LOG.info(GlobalVal.CONST_RESEND_SIGN_REQUEST_VIA_WA_SEND);
		} else {
			LOG.info(GlobalVal.CONST_RESEND_SIGN_REQUEST_VIA_WA_SEND);
		}
	}
	
private void resendSignRequestWaHalosis(MsTenant tenant, Map<String, Object> templateParameters, String email, TrDocumentD document, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);
		TrDocumentH docH = document.getTrDocumentH();
		Map<String, Object> docMap = new HashMap<>();
		docMap.put("refNumber", docH.getRefNumber());
		
		templateParameters.put("doc", docMap);
		
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, "sign_link_invitation_without_password");
		
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(tenant.getTenantName());
		bodyTexts.add(document.getDocumentId());
		
		List<String> headerTexts = new ArrayList<>();
		headerTexts.add(tenant.getTenantName());
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(),
						document.getMsVendor().getVendorCode());

		String phoneNumber = personalDataEncLogic.decryptToString(vuser.getPhoneBytea());
		
		String reservedTrxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		
		HalosisSendWhatsAppRequestBean request = new HalosisSendWhatsAppRequestBean();
		request.setBodyTexts(bodyTexts);
		request.setMsTenant(tenant);
		request.setTemplate(template);
		request.setAmMsuser(user);
		request.setReservedTrxNo(reservedTrxNo);
		request.setPhoneNumber(phoneNumber);
		request.setTrDocumentH(docH);
		request.setMsBusinessLine(docH.getMsBusinessLine());
		request.setMsOffice(docH.getMsOffice());
		request.setRefNo(docH.getRefNumber());
		request.setHeaderTexts(headerTexts);
		request.setNotes(phoneNumber + " : Resend WhatsApp Sign Request");
		
		if ("1".equals(gs.getGsValue())) {
			whatsAppHalosisLogic.sendMessage(request, auditTrailBean, audit);
			LOG.info(GlobalVal.CONST_RESEND_SIGN_REQUEST_VIA_WA_SEND);
		} else {
			LOG.info(GlobalVal.CONST_RESEND_SIGN_REQUEST_VIA_WA_SEND);
		}
	}
	
	private void resendSignRequestSmsJatis(MsTenant tenant, String link, Map<String, Object> templateParameters, String email, TrDocumentD document, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);
		MsOffice office = document.getTrDocumentH().getMsOffice();
		MsBusinessLine businessLine = document.getTrDocumentH().getMsBusinessLine();
		MsVendorRegisteredUser vUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, document.getMsVendor().getVendorCode());
		String phoneNumber = personalDataEncLogic.decryptToString(vUser.getPhoneBytea());
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		String refNumber = document.getTrDocumentH().getRefNumber();
		String notes = phoneNumber + " : Resend SMS Sign Request";
		
		Map<String, Object> userParam = new HashMap<>();
		userParam.put(MAP_KEY_TENANT, tenant.getTenantName());
		userParam.put("link", link);
		templateParameters.put("user", userParam);
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS, templateParameters);
		
		JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, office, businessLine, phoneNumber, template.getBody(), trxNo, refNumber, false);
		if ("1".equals(gs.getGsValue())) {
			LOG.info("Resend sign request via SMS JATIS Sent.");
			jatisSmsLogic.sendSmsAndCutBalance(request, document.getTrDocumentH(), document, vUser.getAmMsuser(), notes, audit, auditTrailBean);
		} else {
			LOG.info("Resend sign request via SMS JATIS Sent.");
		}
		
	}
	
	private void resendSignRequestSms(MsTenant tenant, String link, Map<String, Object> templateParameters, String email, TrDocumentD document, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		Map<String, Object> userParam = new HashMap<>();
		userParam.put(MAP_KEY_TENANT, tenant.getTenantName());
		userParam.put("link", link);
		templateParameters.put("user", userParam);
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS, templateParameters);

		boolean checkUserExistence = true;
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, checkUserExistence, audit);
		
		MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(),
						document.getMsVendor().getVendorCode());

		String phoneNumber = personalDataEncLogic.decryptToString(vuser.getPhoneBytea());

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		String notes = phoneNumber + " : Resend SMS Sign Request";

		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNumber, template.getBody(), tenant);
		SendSmsResponse smsResponse =new  SendSmsResponse();
		if ("1".equals(gs.getGsValue())) {
			smsResponse = smsLogic.sendSms(sendSmsValueFirstRequestBean, auditTrailBean);
			LOG.info("Resend sign request via SMS VFirst Sent.");
		} else {
			LOG.info("Resend sign request via SMS VFirst Sent.");
		}

		MsLov lovNotificationVendor = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
		if (smsResponse.getErrorCode() == null || 
				(!smsResponse.getErrorCode().equals("28682")
				&& !smsResponse.getErrorCode().equals("28681")
				&& !smsResponse.getErrorCode().equals("408"))) {
			saldoLogic.insertBalanceMutation(null, document.getTrDocumentH(), document, balanceType, trxType, tenant, vendor, new Date(), 
					document.getTrDocumentH().getRefNumber(), -1, String.valueOf(smsResponse.getTrxNo()), user, notes, smsResponse.getGuid(), audit);
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, String.valueOf(smsResponse.getTrxNo()), smsResponse.getGuid(), phoneNumber, 
					NotificationType.SMS_VFIRST, lovNotificationVendor, auditTrailBean.getLovSendingPoint(), audit);
		} else {
			saldoLogic.insertBalanceMutation(null, document.getTrDocumentH(), document, balanceType, trxType,
					tenant, vendor, new Date(), document.getTrDocumentH().getRefNumber(), 0, String.valueOf(smsResponse.getTrxNo()), user,
					notes + ERROR + smsResponse.getErrorCode(), smsResponse.getGuid(), audit);
		}
	}

	private void validateNotificationLimitByPeriod(TrDocumentH docH, MsLov sendingPoint, MsTenant tenant, String phoneNumber, AuditContext audit) {
		String timePeriodLimit = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_LIMIT_NOTIF_TIME_PERIOD, "0");
		if ("0".equals(timePeriodLimit)) {
			timePeriodLimit = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_LIMIT_NOTIF_TIME_PERIOD);
		}
		int periodLimitMinutes = Integer.parseInt(timePeriodLimit);

		String notificationLimit = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_LIMIT_SEND_NOTIF_BY_PERIOD, "0");
		if ("0".equals(notificationLimit)) {
			notificationLimit = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_LIMIT_SEND_NOTIF_BY_PERIOD);
		}
		int notificationLimitInt = Integer.parseInt(notificationLimit);

		Date periodStartTime = Tool.addMinuteFromNow(-periodLimitMinutes);

		int notificationCount = daoFactory.getMessageDeliveryReportDao().countNotificationByPeriod(docH, sendingPoint, periodStartTime, phoneNumber);

		if (notificationCount >= notificationLimitInt) {
			throw new SendNotificationException(
				getMessage("businesslogic.global.limitnotification", new Object[] { periodLimitMinutes }, audit), ReasonSendNotif.NOTIF_PERIOD_LIMIT_REACHED);
		}
    }

	private void validateNotificationDailyAttempt(TrDocumentH docH, MsTenant tenant, AuditContext audit) {
        int dailyLimit = tenantSettingsLogic.getSettingValue(tenant, "MAX_NOTIF_SENDING_POINT_DAILY", 0);
        if (dailyLimit == 0) {
            String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode("MAX_NOTIF_SENDING_POINT_DAILY");
            try {
                dailyLimit = Integer.parseInt(gsValue);
            } catch (Exception e) {
                dailyLimit = 0;
            }
        }
        if (dailyLimit == 0) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitnotset", null, audit), ReasonSendNotif.NOTIF_LIMIT_NOT_SET);
        }

        if (docH.getNotificationAttemptDate() == null || docH.getNotificationAttemptNum() == null || !DateUtils.isSameDay(docH.getNotificationAttemptDate(), new Date())) {
            docH.setNotificationAttemptNum((short) 0);
        }

		if (docH.getNotificationAttemptNum() >= dailyLimit) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitreached", null, audit), ReasonSendNotif.NOTIF_DAILY_LIMIT_REACHED);
        }

        docH.setNotificationAttemptNum((short) (docH.getNotificationAttemptNum() + 1));
        docH.setNotificationAttemptDate(new Date());

        docH.setUsrUpd(audit.getCallerId());
        docH.setDtmUpd(new Date());
        daoFactory.getDocumentDao().updateDocumentH(docH);
    }

	@Override
	public ViewSignerEmbedResponse viewSignerEmbed(ViewSignerEmbedRequest request, AuditContext audit) {
		
		EmbedMsgBeanV2 embedMsgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		
		String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), embedMsgBean.getMsTenant().getAesEncryptKey(), audit);
		audit.setCallerId(embedMsgBean.getDecryptedEmail());
		
		if (!request.isMonitoring()) {
			SignerListRequest signerListRequest = new SignerListRequest();
			signerListRequest.setDocumentId(documentId);
			if (request.isOffice()) {
				signerListRequest.setOfficeCode(embedMsgBean.getMsOffice().getOfficeCode());
			}
			this.validateSignerListRequest(audit);
		}
		
		ViewSignerEmbedResponse response = new ViewSignerEmbedResponse();
		List<ViewSignerListBean> signerBeanList = new ArrayList<>();
		
		List<Map<String, Object>> signerList = daoFactory.getDocumentDao().getDocumentSignerList(documentId);
		Iterator<Map<String, Object>> itr = signerList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			ViewSignerListBean bean = new ViewSignerListBean();
			bean.setSignerType((String) map.get("d1"));
			bean.setSignerName((String) map.get("d2"));
			bean.setSignerPhone((String) map.get("d3"));
			bean.setSignerEmail((String) map.get("d0"));
			bean.setSignStatus((String) map.get("d4"));
			bean.setSignDate((String) map.get("d5"));
			bean.setRegisterStatus(this.getSignerRegistrationStatus((String) map.get("d0"), documentId));
			signerBeanList.add(bean);
		}
		response.setListSigner(signerBeanList);
		response.setTotalResult(signerList.size());
		return response;
	}
	
	private void validateSignerListRequest(AuditContext audit) {
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
	}
	
	private String getSignerRegistrationStatus(String email, String documentId) {
		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email,
						document.getMsVendor().getVendorCode());

		if (null == vendorUser) {
			return USER_STATUS_NOT_REGISTERED;
		}

		if ("0".equals(vendorUser.getIsRegistered()) && "0".equals(vendorUser.getIsActive())) {
			return USER_STATUS_NOT_REGISTERED;
		} else if ("1".equals(vendorUser.getIsRegistered()) && "0".equals(vendorUser.getIsActive())) {
			return USER_STATUS_NOT_ACTIVATED;
		} else if ("1".equals(vendorUser.getIsRegistered()) && "1".equals(vendorUser.getIsActive())) {
			return USER_STATUS_ACTIVATED;
		}
		return USER_STATUS_UNKNOWN;
	}


	@Override
	public CheckDocumentSendStatusResponse checkDocumentSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request,
			AuditContext audit) {
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);

		String idDoc = commonLogic.decryptMessageToString(request.getDocumentId(), msg.getMsTenant().getAesEncryptKey(), audit);


		CheckDocumentSendStatusEmbedRequest checkrequest = new CheckDocumentSendStatusEmbedRequest();
		checkrequest.setDocumentId(idDoc);

		return documentLogic.checkDocSendStatus(checkrequest, audit);
	}

	@Override
	public BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request, AuditContext audit) throws IOException, ParseException {
		EmbedMsgBeanV2 msgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		audit.setCallerId(msgBean.getDecryptedEmail());
		
		MsTenant tenant = msgBean.getMsTenant();
		String documentIdDecrypt = commonLogic.decryptMessageToString(request.getEncryptedDocumentIds()[0], tenant.getAesEncryptKey(),audit);
		TrDocumentD docD = null;
		if (documentIdDecrypt != null) {
			docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentIdDecrypt);
		}
		BulkSignDocumentRequest bulkSignRequest = new BulkSignDocumentRequest();
		BulkSignDocumentResponse response = new BulkSignDocumentResponse();
		Status statusSign = new Status();

		if (null == docD) {
			statusSign.setCode(4002);
			statusSign.setMessage(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND_1,
					new Object[] { audit.getCallerId() }, this.retrieveLocaleAudit(audit)));

			insertErrorHistoryBulkSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
					msgBean.getDecryptedEmail(), msgBean.getAmMsuser(), msgBean.getMsTenant().getTenantCode(),msgBean.getMsOffice().getMsRegion().getRegionName(),msgBean.getMsOffice().getOfficeName(),
					audit);

			response.setStatus(statusSign);
			return response;
		}

		if(tenant != docD.getMsTenant() ) {
			throw new DocumentException(getMessage("businesslogic.document.docnotfounds", null, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}
		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(msgBean.getAmMsuser().getIdMsUser(), docD.getMsVendor().getVendorCode());
		
		String emailInRequest = registeredUser.getSignerRegisteredEmail();

		if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
			throw new DocumentException(
					getMessage("businesslogic.document.cannotsignwithvendor",
							new String[] { docD.getMsVendor().getVendorName() }, audit),
					ReasonDocument.INVALID_DOCUMENT_VENDOR);
		} else if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
			List<String> decryptedDocumentIds = new ArrayList<>();
			for (String documentId : request.getEncryptedDocumentIds()) {
				String decryptedDocumentId = commonLogic.decryptMessageToString(documentId,tenant.getAesEncryptKey() ,audit);
				TrDocumentD docTemp = daoFactory.getDocumentDao().getDocumentDetailByDocId(decryptedDocumentId);
				if (!docD.getMsVendor().getVendorCode().equals(docTemp.getMsVendor().getVendorCode())) {
					throw new DocumentException(
							getMessage("businesslogic.document.documentvendornotmatch", null, audit),
							ReasonDocument.INVALID_DOCUMENT_VENDOR);
				}
				decryptedDocumentIds.add(decryptedDocumentId);
			}
			bulkSignRequest.setLoginId(emailInRequest);
			bulkSignRequest.setDocumentIds(decryptedDocumentIds.toArray(new String[0]));

		} else {
			throw new DocumentException(
					getMessage("businesslogic.document.cannotsignwithvendor",
							new String[] { docD.getMsVendor().getVendorName() }, audit),
					ReasonDocument.INVALID_DOCUMENT_VENDOR);
		}

		return documentLogic.bulkSignDocumentWithoutSecurity(bulkSignRequest, audit);
	}
	
	@Override
	public StartStampingMeteraiEmbedResponse startStampingMeteraiEmbed(StartStampingMeteraiEmbedRequest request, AuditContext audit) {
		
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY, null, audit), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_TENANT_NOT_FOUND, new String[] {request.getTenantCode()}, audit), 
					ReasonTenant.TENANT_NOT_FOUND);
		}		
		
		EmbedMsgBeanV2 embedMsgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		audit.setCallerId(embedMsgBean.getDecryptedEmail());
		
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), tenant.getTenantCode());
		if (null == docH) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND,
					new String[] {tenant.getRefNumberLabel(), request.getRefNumber()}, audit), ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}
		
		documentValidatorLogic.validateAgreementForStartStampEmbed(docH, tenant, audit);
		
		String lovStampingTenant = "";
		
		if (tenant.getLovVendorStamping() != null  && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(tenant.getLovVendorStamping().getCode()) ) {
			lovStampingTenant = GlobalVal.ON_PREM_STAMP_IN_QUEUE_PRIVY;
		} else if (tenant.getLovVendorStamping() != null  && GlobalVal.VENDOR_CODE_VIDA.equals(tenant.getLovVendorStamping().getCode()) ) {
			lovStampingTenant = GlobalVal.VIDA_STAMP_IN_QUEUE;
		} else {
			lovStampingTenant = GlobalVal.ON_PREM_STAMP_IN_QUEUE;
		}
		
		docH.setProsesMaterai(new Short( lovStampingTenant));
		docH.setDtmUpd(new Date());
		docH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(docH);

		
		Status status = new Status();
		status.setCode(0);
		status.setMessage("Proses stamping dimulai!");
		
		StartStampingMeteraiEmbedResponse response = new StartStampingMeteraiEmbedResponse();
		response.setStatus(status);
		return response;
		
	}
	
	
	private void insertErrorHistoryBulkSignDocumentEmbed(String msg, String errType, TrDocumentD docD, String email,
			AmMsuser user, String tenantCode, String regionName, String officeName, AuditContext audit) {
		String refNo = null;
		String bizLine = null;
		String office = officeName;
		String region = regionName;
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCodeNewTrx(tenantCode);
		MsVendor vendor = null;
		if (null != docD) {
			TrDocumentH docH = docD.getTrDocumentH();
			refNo = docH.getRefNumber();
			bizLine = docH.getMsBusinessLine().getBusinessLineName();
			office = docH.getMsOffice().getOfficeName();
			region = docH.getMsOffice().getMsRegion().getRegionName();
			tenant = docD.getMsTenant();
			vendor = docD.getMsVendor();
		}

		SignerBean cust = new SignerBean();
		if (null != user) {
			PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUserOptional(user.getIdMsUser(), true, false,
					false, false, false);
			cust.setUserName(user.getFullName());
			cust.setIdNo(pd.getIdNoRaw());
		} else {
			cust.setUserName(email);
		}

		inserErrorHistory(bizLine, region, office, refNo, cust, null, null, msg, errType, tenant, vendor,
				GlobalVal.CODE_LOV_ERR_HIST_MODULE_SIGN_DOC, audit);

	}
	private void inserErrorHistory(String bizLine, String region, String office, String refNo, SignerBean cust,
			SignerBean sps, SignerBean grt, String msg, String errType, MsTenant tenant, MsVendor vendor, String module,
			AuditContext audit) {
		MsLov lovModul = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_ERR_HIST_MODULE, module);

		TrErrorHistory errorHistory = new TrErrorHistory();
		errorHistory.setMsLov(lovModul);
		errorHistory.setCustName(StringUtils.upperCase(cust.getUserName()));
		errorHistory.setCustIdno(cust.getIdNo());
		if (null != sps) {
			errorHistory.setSpsName(StringUtils.upperCase(sps.getUserName()));
			errorHistory.setSpsIdno(sps.getIdNo());
		}

		if (null != grt) {
			errorHistory.setGrtName(StringUtils.upperCase(grt.getUserName()));
			errorHistory.setGrtIdno(grt.getIdNo());
		}
		errorHistory.setBusinessLine(StringUtils.upperCase(bizLine));
		errorHistory.setRefNumber(StringUtils.upperCase(refNo));
		errorHistory.setRegion(StringUtils.upperCase(region));
		errorHistory.setOffice(StringUtils.upperCase(office));
		errorHistory.setMsTenant(tenant);
		errorHistory.setMsVendor(vendor);
		errorHistory.setErrorType(errType);
		errorHistory.setErrorDate(new Date());
		errorHistory.setErrorMessage(StringUtils.left(msg, 300));
		errorHistory.setUsrCrt(audit.getCallerId());
		errorHistory.setDtmCrt(new Date());
		errorHistory.setRerunProcess("0");
		daoFactory.getErrorHistoryDao().insertErrorHistory(errorHistory);
	}
	
	private void validateResendNotifSignConcurrently(String docId, AuditContext audit) {
    	LOG.info("Checking Resend Notif Sign Document ID: {}", docId);
		LOG.info("Resend Notif Sign Document ID set size: {} (before validation)", resendNotifSignSet.size());
		if (!resendNotifSignSet.add(docId)) {
			throw new DocumentException(getMessage("businesslogic.document.currentlyprocessed", null, audit), ReasonDocument.DOCUMENT_STILL_PROCESSING);
		}
	}
	
}
